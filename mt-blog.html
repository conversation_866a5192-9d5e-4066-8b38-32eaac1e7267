<!DOCTYPE html><html lang="en"><head><meta charset="utf-8" /><meta property="og:site_name" content="" /><meta property="og:type" content="article" /><meta property="og:url" content="/mt-blog" /><meta content="summary_large_image" name="twitter:card" />          <meta property="og:title" content="Blog / Άρθρα" />     <meta name="twitter:title" content="Blog / Άρθρα" />      <meta property="og:description" content="Άρθρα και ενημερώσεις για τη Μουσικοθεραπεία." />     <meta name="twitter:description" content="Άρθρα και ενημερώσεις για τη Μουσικοθεραπεία." />      <meta property="og:image" content="assets/d5735b95679647ba002fb1d4923d3d7d.webp" />     <meta property="twitter:image" content="assets/d5735b95679647ba002fb1d4923d3d7d.webp">      <title>Blog / Άρθρα</title>      <meta name="description" content="Άρθρα και ενημερώσεις για τη Μουσικοθεραπεία." />            <link rel="canonical" href="/mt-blog" /><link rel="icon" href="https://cloud-1de12d.b-cdn.net/media/iW=32%26iH=any/5800e034b05aa57cedaa6e50ab5154b2.png" sizes="32x32"/><link rel="icon" href="https://cloud-1de12d.b-cdn.net/media/iW=192%26iH=any/5800e034b05aa57cedaa6e50ab5154b2.png" sizes="192x192"/><link rel="apple-touch-icon-precomposed" href="https://cloud-1de12d.b-cdn.net/media/iW=180&iH=any/5800e034b05aa57cedaa6e50ab5154b2.png"/><meta name="viewport" content="width=device-width, initial-scale=1"><link class="brz-link brz-link-bunny-fonts-prefetch" rel="dns-prefetch" href="//fonts.bunny.net"><link class="brz-link brz-link-bunny-fonts-preconnect" rel="preconnect" href="https://fonts.bunny.net/" crossorigin><link class="brz-link brz-link-cdn-preconnect" rel="preconnect" href="https://cloud-1de12d.b-cdn.net" crossorigin><link href="https://fonts.bunny.net/css?family=Comfortaa:300,regular,500,600,700|Lato:100,100italic,300,300italic,regular,italic,700,700italic,900,900italic&subset=arabic,bengali,cyrillic,cyrillic-ext,devanagari,greek,greek-ext,gujarati,hebrew,khmer,korean,latin-ext,tamil,telugu,thai,vietnamese&display=swap" class="brz-link brz-link-google" type="text/css" rel="stylesheet"/><link href="assets/eb37fe33c69c8176a947e723f64dd277.css" class="brz-link brz-link-preview-lib" data-brz-group="group-1_3" rel="stylesheet"/><link href="assets/07575d7e3674e1a8b8d8a30025ca06cb.css" class="brz-link brz-link-preview-lib-pro" data-brz-group="group-1_2" rel="stylesheet"/><link href="assets/a1c351b066e704f38f6ab6530f000598.css" class="brz-link brz-link-preview-pro" rel="stylesheet"/><style class="brz-style">.brz .brz-css-q1tgtc {z-index: auto;margin: 0;}
.brz .brz-css-q1tgtc.brz-section .brz-section__content {min-height: auto;display: flex;}
.brz .brz-css-q1tgtc .brz-container {justify-content: center;}
.brz .brz-css-q1tgtc > .slick-slider > .brz-slick-slider__dots {color: rgba(0,0,0,1);}
.brz .brz-css-q1tgtc > .slick-slider > .brz-slick-slider__arrow {color: rgba(0,0,0,.7);}
@media (min-width:991px) {.brz .brz-css-q1tgtc {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-q1tgtc {display: block;}}
@media (max-width:767px) {.brz .brz-css-q1tgtc {display: block;}}
.brz .brz-css-9fvlif {margin: -100px 0px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-9fvlif {z-index: auto;margin: -100px 0px 0px 0px;}
	.brz .brz-css-9fvlif.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-9fvlif .brz-container {justify-content: center;}
	.brz .brz-css-9fvlif > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-9fvlif > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-9fvlif:hover {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-9fvlif {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-9fvlif {margin: 0;}}
.brz .brz-css-7pdknx {padding: 75px 0px 75px 0px;}
.brz .brz-css-7pdknx > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-7pdknx > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
.brz .brz-css-7pdknx > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}
@media (min-width:991px) {.brz .brz-css-7pdknx > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-7pdknx {padding: 50px 15px 50px 15px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-7pdknx > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-7pdknx {padding: 25px 15px 25px 15px;}}
@media (max-width:767px) {.brz .brz-css-7pdknx > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-7pdknx > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-19bezg3 {padding: 179px 0px 128px 0px;}
.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-image {background-image: url("assets/a3770d4abd6b2cfabae1b8969db0beeb.webp");background-position: 52% 36%;}
.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-image:after {content: "";background-image: url("assets/a3770d4abd6b2cfabae1b8969db0beeb.webp");}
.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-color {background-color: transparent;background-image: linear-gradient(25deg,rgba(var(--brz-global-color2),.71) 0%,rgba(var(--brz-global-color4),1) 100%);}
@media (min-width:991px) {.brz .brz-css-19bezg3 {padding: 179px 0px 128px 0px;}
	.brz .brz-css-19bezg3 > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-19bezg3:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-image {background-image: url("assets/a3770d4abd6b2cfabae1b8969db0beeb.webp");filter: none;background-position: 52% 36%;display: block;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: url("assets/a3770d4abd6b2cfabae1b8969db0beeb.webp");}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-color {background-color: transparent;background-image: linear-gradient(25deg,rgba(var(--brz-global-color2),.71) 0%,rgba(var(--brz-global-color4),1) 100%);}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-19bezg3 > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-19bezg3:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-19bezg3:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-19bezg3 {padding: 128px 16px 128px 1px;}}
@media (max-width:767px) {.brz .brz-css-19bezg3 {padding: 64px 20px 64px 20px;}}
.brz .brz-css-1ap2zsh {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1ap2zsh {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1ap2zsh {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-1ap2zsh {max-width: 100%;}}
@media (min-width:991px) {.brz .brz-css-g3ty65 {max-width: calc(.9 * var(--brz-section-container-max-width,1170px));}}
@media (min-width:991px) {.brz .brz-css-g3ty65:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-g3ty65 {max-width: calc(.9 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-g3ty65 {max-width: 75%;}}
@media (max-width:767px) {.brz .brz-css-g3ty65 {max-width: 90%;}}
.brz .brz-css-14xvecl {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-14xvecl .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-14xvecl {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-14xvecl {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-14xvecl {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-hio4wl {margin: 0;}
@media (min-width:991px) {.brz .brz-css-hio4wl {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-hio4wl .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-hio4wl {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-18p45dg {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-1xd4var {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-xQy3I {margin-top: 0px !important;margin-bottom: 0px !important;text-align: center !important;font-family: var(--brz-heading1fontfamily,initial) !important;font-size: var(--brz-heading1fontsize,initial);line-height: var(--brz-heading1lineheight,initial);font-weight: var(--brz-heading1fontweight,initial);font-weight: var(--brz-heading1bold,initial);letter-spacing: var(--brz-heading1letterspacing,initial);font-variation-settings: var(--brz-heading1fontvariation,initial);font-style: var(--brz-heading1italic,initial);text-decoration: var(--brz-heading1textdecoration,initial) !important;text-transform: var(--brz-heading1texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-xQy3I {margin-top: 0px !important;margin-bottom: 0px !important;text-align: center !important;font-family: var(--brz-heading1fontfamily,initial) !important;font-size: var(--brz-heading1fontsize,initial);line-height: var(--brz-heading1lineheight,initial);font-weight: var(--brz-heading1fontweight,initial);font-weight: var(--brz-heading1bold,initial);letter-spacing: var(--brz-heading1letterspacing,initial);font-variation-settings: var(--brz-heading1fontvariation,initial);font-style: var(--brz-heading1italic,initial);text-decoration: var(--brz-heading1textdecoration,initial) !important;text-transform: var(--brz-heading1texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-xQy3I {font-size: var(--brz-heading1tabletfontsize,initial);line-height: var(--brz-heading1tabletlineheight,initial);font-weight: var(--brz-heading1tabletfontweight,initial);font-weight: var(--brz-heading1tabletbold,initial);letter-spacing: var(--brz-heading1tabletletterspacing,initial);font-variation-settings: var(--brz-heading1tabletfontvariation,initial);font-style: var(--brz-heading1tabletitalic,initial);text-decoration: var(--brz-heading1tablettextdecoration,initial) !important;text-transform: var(--brz-heading1tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-xQy3I {font-size: var(--brz-heading1mobilefontsize,initial);line-height: var(--brz-heading1mobilelineheight,initial);font-weight: var(--brz-heading1mobilefontweight,initial);font-weight: var(--brz-heading1mobilebold,initial);letter-spacing: var(--brz-heading1mobileletterspacing,initial);font-variation-settings: var(--brz-heading1mobilefontvariation,initial);font-style: var(--brz-heading1mobileitalic,initial);text-decoration: var(--brz-heading1mobiletextdecoration,initial) !important;text-transform: var(--brz-heading1mobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1njd3gj {z-index: auto;margin: 0;}
	.brz .brz-css-1njd3gj.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-1njd3gj .brz-container {justify-content: center;}
	.brz .brz-css-1njd3gj > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-1njd3gj > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-1njd3gj:hover {display: block;}}
.brz .brz-css-mxpdf1 {padding: 108px 48px 108px 48px;}
.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-color {background-color: rgba(229,237,233,1);}
@media (min-width:991px) {.brz .brz-css-mxpdf1 {padding: 108px 48px 108px 48px;}
	.brz .brz-css-mxpdf1 > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-mxpdf1:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-color {background-color: rgba(229,237,233,1);background-image: none;}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-mxpdf1 > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-mxpdf1:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-mxpdf1:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-mxpdf1 {padding: 50px 40px 16px 40px;}}
@media (max-width:767px) {.brz .brz-css-mxpdf1 {padding: 24px;}}
@media (min-width:991px) {.brz .brz-css-2g5ayj:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-2g5ayj {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-1ylkdyc {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-1ylkdyc > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-1ylkdyc > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-1ylkdyc > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1ylkdyc {min-height: auto;display: flex;}
	.brz .brz-css-1ylkdyc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1ylkdyc {min-height: auto;display: flex;}
	.brz .brz-css-1ylkdyc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-1ylkdyc {min-height: auto;display: flex;}
	.brz .brz-css-1ylkdyc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ylkdyc > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (min-width:991px) {.brz .brz-css-1ss6de9 {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-1ss6de9 > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-1ss6de9:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1ss6de9 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1ss6de9:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1ss6de9:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1ss6de9 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1ss6de9:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1ss6de9 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1ss6de9:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1ss6de9 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1ss6de9:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1ss6de9:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1ss6de9 {min-height: auto;display: flex;}
	.brz .brz-css-1ss6de9:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ss6de9:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ss6de9:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ss6de9:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ss6de9:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-bwj1w {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bwj1w {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-bwj1w {padding: 0;}}
.brz .brz-css-dj0192 {padding: 0;}
@media (min-width:991px) {.brz .brz-css-1y28zjb {padding: 0;max-width: 100%;}}
.brz .brz-css-jbjqyi {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-jbjqyi .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-jbjqyi > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-jbjqyi > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-jbjqyi > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-jbjqyi > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-jbjqyi {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-jbjqyi > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-jbjqyi > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbjqyi > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1s8u9cv {flex: 1 1 64.6%;max-width: 64.6%;}
.brz .brz-css-1s8u9cv > .brz-bg {margin: 0px 12px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1s8u9cv {z-index: auto;flex: 1 1 64.6%;max-width: 64.6%;justify-content: flex-start;}
	.brz .brz-css-1s8u9cv .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-1s8u9cv > .brz-bg {margin: 0px 12px 0px 0px;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-1s8u9cv:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1s8u9cv > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1s8u9cv:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1s8u9cv:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1s8u9cv > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1s8u9cv:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1s8u9cv > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1s8u9cv:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1s8u9cv > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1s8u9cv:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1s8u9cv:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1s8u9cv:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1s8u9cv:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1s8u9cv {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1s8u9cv > .brz-bg {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-1s8u9cv {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1s8u9cv > .brz-bg {margin: 0;}}
.brz .brz-css-571mf8 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-571mf8 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-571mf8 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-571mf8 {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-571mf8 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-qrjbxt {margin: 0px 12px 0px 0px;padding: 0;}
@media (min-width:991px) {.brz .brz-css-qrjbxt {z-index: auto;margin: 0px 12px 0px 0px;border: 0px solid transparent;padding: 0;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-qrjbxt:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-qrjbxt {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-qrjbxt {margin: 0;}}
@media (min-width:991px) {.brz .brz-css-37d1g5 {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-37d1g5 > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-37d1g5:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-37d1g5 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-37d1g5:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-37d1g5:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-37d1g5 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-37d1g5:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-37d1g5 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-37d1g5:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-37d1g5 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-37d1g5:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-37d1g5:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-37d1g5 {min-height: auto;display: flex;}
	.brz .brz-css-37d1g5:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-37d1g5:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-37d1g5:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-37d1g5:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-37d1g5:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-5ecxbm {padding: 0;max-width: 100%;}}
.brz .brz-css-1hmd296 {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-1hmd296 > .brz-bg {margin: 0;border-radius: 12px;}
.brz .brz-css-1hmd296 > .brz-bg {box-shadow: 0px 0px 60px 10px rgba(0,0,0,.07);}
.brz .brz-css-1hmd296 > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);}
@media (min-width:991px) {.brz .brz-css-1hmd296 {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-1hmd296 .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-1hmd296 > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 12px;}
	.brz .brz-css-1hmd296:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: 0px 0px 60px 0px rgba(var(--brz-global-color7),.22);}
	.brz .brz-css-1hmd296 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1hmd296:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1hmd296:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1hmd296 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1hmd296:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);background-image: none;}
	.brz .brz-css-1hmd296 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1hmd296:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1hmd296 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1hmd296:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1hmd296:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1hmd296:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1hmd296:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1hmd296 > .brz-bg {margin: 0px 0px 24px 0px;}}
.brz .brz-css-1dam7wr {animation-name: none;animation-duration: 1000ms;animation-delay: 1000ms;animation-iteration-count: unset;}
.brz .brz-css-1v3th81 {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}
@media (min-width:991px) {.brz .brz-css-1v3th81 {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}}
.brz .brz-css-cvpvfe {margin: 0;padding: 40px;}
@media (min-width:991px) {.brz .brz-css-cvpvfe {z-index: auto;margin: 0;border: 0px solid transparent;padding: 40px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-cvpvfe:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-cvpvfe {padding: 32px;}}
@media (max-width:767px) {.brz .brz-css-cvpvfe {margin: 0px 0px 24px 0px;padding: 24px;}}
.brz .brz-css-flz70m {margin: 10px 0px 10px 0px;}
@media (min-width:991px) {.brz .brz-css-flz70m {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
	.brz .brz-css-flz70m .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-flz70m {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-flz70m {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-flz70m {margin: 0;}}
.brz .brz-css-16faig1:not(.brz-image--hovered) {max-width: 100%;}
.brz .brz-css-16faig1 {height: auto;border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-16faig1 {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-16faig1 .brz-picture:after {border-radius: 0px;}
.brz .brz-css-16faig1 .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
.brz .brz-css-16faig1 .brz-picture {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-16faig1 .brz-picture {filter: none;}
@media (min-width:991px) {.brz .brz-css-16faig1 {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1 .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1 .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1.brz-image--withHover img.brz-img, .brz .brz-css-16faig1.brz-image--withHover img.dynamic-image, .brz .brz-css-16faig1.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-16faig1 {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1 .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1 .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1.brz-image--withHover img.brz-img, .brz .brz-css-16faig1.brz-image--withHover img.dynamic-image, .brz .brz-css-16faig1.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-16faig1 {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1 .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1 .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16faig1.brz-image--withHover img.brz-img, .brz .brz-css-16faig1.brz-image--withHover img.dynamic-image, .brz .brz-css-16faig1.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-8x7zlp {border-radius: 12px;}
.brz .brz-css-8x7zlp .brz-picture:after {border-radius: 12px;}
@media (min-width:991px) {.brz .brz-css-8x7zlp:not(.brz-image--hovered) {max-width: 100%;}
	.brz .brz-css-8x7zlp {height: auto;border-radius: 12px;mix-blend-mode: normal;}
	.brz .brz-css-8x7zlp:hover {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-8x7zlp .brz-picture:after {border-radius: 12px;}
	.brz .brz-css-8x7zlp:hover .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
	.brz .brz-css-8x7zlp .brz-picture {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-8x7zlp:hover .brz-picture {filter: none;}}
@media (min-width:991px) {.brz .brz-css-8x7zlp:hover {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-8x7zlp:hover .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-8x7zlp:hover .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-8x7zlp.brz-image--withHover img.brz-img, .brz .brz-css-8x7zlp.brz-image--withHover img.dynamic-image, .brz .brz-css-8x7zlp.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-jpxdhz.brz-hover-animation__container {max-width: 100%;}
@media (min-width:991px) {.brz .brz-css-167pr3h.brz-hover-animation__container {max-width: 100%;}}
.brz .brz-css-g279k7 {padding-top: 56.2504%;}
.brz .brz-css-g279k7 > .brz-img {position: absolute;width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-g279k7 {padding-top: 56.25%;}}
@media (max-width:767px) {.brz .brz-css-g279k7 {padding-top: 56.2513%;}}
.brz .brz-css-1ly5fak {padding-top: 0;}
.brz .brz-css-1ly5fak > .brz-img {position: inherit;}
@media (min-width:991px) {.brz .brz-css-1ly5fak {padding-top: 0;}
	.brz .brz-css-1ly5fak > .brz-img {position: inherit;width: 100%;}}
.brz .brz-css-59qxu7 {width: 885.1px;height: 497.87px;margin-left: -110.65px;margin-top: 0px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-59qxu7 {width: 938.72px;height: 528.03px;margin-left: -117.36px;}}
@media (max-width:767px) {.brz .brz-css-59qxu7 {width: 509.35px;height: 286.51px;margin-left: -63.68px;}}
.brz .brz-css-1yrul57 {width: 100%;height: auto;margin-left: auto;margin-top: auto;}
@media (min-width:991px) {.brz .brz-css-1yrul57 {width: 100%;height: auto;margin-left: auto;margin-top: auto;}}
.brz .brz-css-5syyht {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1yowkui {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1yowkui .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1yowkui {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-z4dbzf {height: 50px;}
.brz .brz-css-7mq8k {height: 34px;}
@media (min-width:991px) {.brz .brz-css-7mq8k {height: 34px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-7mq8k {height: 25px;}}
@media (max-width:767px) {.brz .brz-css-7mq8k {height: 25px;}}
@media (min-width:991px) {.brz .brz-css-ah6ygt {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-ah6ygt > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-ah6ygt:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-ah6ygt > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-ah6ygt:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-ah6ygt:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-ah6ygt > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-ah6ygt:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-ah6ygt > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-ah6ygt:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-ah6ygt > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-ah6ygt:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-ah6ygt:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-ah6ygt {min-height: auto;display: flex;}
	.brz .brz-css-ah6ygt:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ah6ygt:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ah6ygt:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ah6ygt:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-ah6ygt:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-12b0c4u {padding: 0;max-width: 100%;}}
.brz .brz-css-10r2txj {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-10r2txj > .brz-bg {margin: 0;}
.brz .brz-css-10r2txj > .brz-bg {border-width: 0px 1px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color7),.3);}
@media (min-width:991px) {.brz .brz-css-10r2txj {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-10r2txj .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-10r2txj > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-10r2txj:hover > .brz-bg {border-width: 0px 1px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color7),.3);box-shadow: none;}
	.brz .brz-css-10r2txj > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-10r2txj:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-10r2txj:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-10r2txj > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-10r2txj:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-10r2txj > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-10r2txj:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-10r2txj > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-10r2txj:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-10r2txj:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10r2txj:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10r2txj:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1hlehkh {margin: 0;border-width: 0px 1px 0px 0px;border-style: solid;border-color: transparent;padding: 0px 15px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1hlehkh {z-index: auto;margin: 0;border-width: 0px 1px 0px 0px;border-style: solid;border-color: transparent;padding: 0px 15px 0px 0px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1hlehkh:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1hlehkh {padding: 0px 10px 0px 0px;}}
.brz .brz-css-1p8qt26 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1p8qt26 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1p8qt26 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1p8qt26 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1s68ja {flex-direction: row;}
.brz .brz-css-1s68ja .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}
.brz .brz-css-1vz21gu .brz-icon__container {margin-left: auto;margin-right: 5px;}
@media (min-width:991px) {.brz .brz-css-1vz21gu {flex-direction: row;}
	.brz .brz-css-1vz21gu .brz-icon__container {margin-left: auto;margin-right: 5px;align-items: flex-start;}}
.brz .brz-css-13audko {font-size: 48px;padding: 0px;border-radius: 0;stroke-width: 1;}
.brz .brz-css-13audko {color: rgba(var(--brz-global-color3),1);border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}
.brz .brz-css-13audko .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-13audko {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-13audko .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-13audko:hover {color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-13audko:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-13audko {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-13audko .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:767px) {.brz .brz-css-13audko {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-13audko .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-12920um {font-size: 17px;}
@media (min-width:991px) {.brz .brz-css-12920um {font-size: 17px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-12920um:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-12920um:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-12920um:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-1270wrn {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-ea64F {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading6fontfamily,initial) !important;font-size: var(--brz-heading6fontsize,initial);line-height: var(--brz-heading6lineheight,initial);font-weight: var(--brz-heading6fontweight,initial);font-weight: var(--brz-heading6bold,initial);letter-spacing: var(--brz-heading6letterspacing,initial);font-variation-settings: var(--brz-heading6fontvariation,initial);font-style: var(--brz-heading6italic,initial);text-decoration: var(--brz-heading6textdecoration,initial) !important;text-transform: var(--brz-heading6texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-ea64F {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading6fontfamily,initial) !important;font-size: var(--brz-heading6fontsize,initial);line-height: var(--brz-heading6lineheight,initial);font-weight: var(--brz-heading6fontweight,initial);font-weight: var(--brz-heading6bold,initial);letter-spacing: var(--brz-heading6letterspacing,initial);font-variation-settings: var(--brz-heading6fontvariation,initial);font-style: var(--brz-heading6italic,initial);text-decoration: var(--brz-heading6textdecoration,initial) !important;text-transform: var(--brz-heading6texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-ea64F {font-size: 13px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
@media (max-width:767px) {.brz .brz-css-ea64F {font-size: 13px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
@media (min-width:991px) {.brz .brz-css-1jm5elg {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1jm5elg .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1jm5elg {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-4yqsm6 {height: 10px;}
@media (min-width:991px) {.brz .brz-css-4yqsm6 {height: 10px;}}
.brz .brz-css-1ef4wpt {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1ef4wpt {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1ef4wpt .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1ef4wpt {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-9rdqdb {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-hx9_H {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading4fontfamily,initial) !important;font-size: var(--brz-heading4fontsize,initial);line-height: var(--brz-heading4lineheight,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-hx9_H {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading4fontfamily,initial) !important;font-size: var(--brz-heading4fontsize,initial);line-height: var(--brz-heading4lineheight,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-hx9_H {font-size: var(--brz-heading4tabletfontsize,initial);line-height: var(--brz-heading4tabletlineheight,initial);font-weight: var(--brz-heading4tabletfontweight,initial);font-weight: var(--brz-heading4tabletbold,initial);letter-spacing: var(--brz-heading4tabletletterspacing,initial);font-variation-settings: var(--brz-heading4tabletfontvariation,initial);font-style: var(--brz-heading4tabletitalic,initial);text-decoration: var(--brz-heading4tablettextdecoration,initial) !important;text-transform: var(--brz-heading4tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-hx9_H {font-size: var(--brz-heading4mobilefontsize,initial);line-height: var(--brz-heading4mobilelineheight,initial);font-weight: var(--brz-heading4mobilefontweight,initial);font-weight: var(--brz-heading4mobilebold,initial);letter-spacing: var(--brz-heading4mobileletterspacing,initial);font-variation-settings: var(--brz-heading4mobilefontvariation,initial);font-style: var(--brz-heading4mobileitalic,initial);text-decoration: var(--brz-heading4mobiletextdecoration,initial) !important;text-transform: var(--brz-heading4mobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-16mk6t1 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-16mk6t1 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-16mk6t1 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1dlpx7r {height: 10px;}
@media (min-width:991px) {.brz .brz-css-1dlpx7r {height: 10px;}}
.brz .brz-css-1y66mil {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1y66mil {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1y66mil .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1y66mil {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-11vio22 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-mGhV0 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-mGhV0 {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-mGhV0 {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-mGhV0 {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-chidof {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-chidof .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-chidof {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-chidof {display: none;}}
@media (max-width:767px) {.brz .brz-css-chidof {display: none;}}
.brz .brz-css-jzezgo {height: 18px;}
@media (min-width:991px) {.brz .brz-css-jzezgo {height: 18px;}}
@media (min-width:991px) {.brz .brz-css-17wbs37 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-17wbs37 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-17wbs37 {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-17wbs37 {display: none;}}
.brz .brz-css-uvrsr {height: 30px;}
@media (min-width:991px) {.brz .brz-css-uvrsr {height: 30px;}}
@media (min-width:991px) {.brz .brz-css-o1xx0d {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-o1xx0d > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-o1xx0d:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-o1xx0d > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-o1xx0d:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-o1xx0d:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-o1xx0d > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-o1xx0d:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-o1xx0d > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-o1xx0d:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-o1xx0d > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-o1xx0d:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-o1xx0d:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-o1xx0d {min-height: auto;display: flex;}
	.brz .brz-css-o1xx0d:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-o1xx0d:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-o1xx0d:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-o1xx0d:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-o1xx0d:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-a4whaz {padding: 0;max-width: 100%;}}
.brz .brz-css-11gfgi5 {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-11gfgi5 > .brz-bg {margin: 0;border-radius: 12px;}
.brz .brz-css-11gfgi5 > .brz-bg {box-shadow: 0px 0px 60px 10px rgba(0,0,0,.07);}
.brz .brz-css-11gfgi5 > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);}
@media (min-width:991px) {.brz .brz-css-11gfgi5 {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-11gfgi5 .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-11gfgi5 > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 12px;}
	.brz .brz-css-11gfgi5:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: 0px 0px 60px 0px rgba(var(--brz-global-color7),.22);}
	.brz .brz-css-11gfgi5 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-11gfgi5:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-11gfgi5:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-11gfgi5 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-11gfgi5:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);background-image: none;}
	.brz .brz-css-11gfgi5 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-11gfgi5:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-11gfgi5 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-11gfgi5:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-11gfgi5:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-11gfgi5:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-11gfgi5:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-ys064w {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}
@media (min-width:991px) {.brz .brz-css-ys064w {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}}
.brz .brz-css-1hc8j29 {margin: 0;padding: 40px;}
@media (min-width:991px) {.brz .brz-css-1hc8j29 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 40px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1hc8j29:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1hc8j29 {padding: 32px;}}
@media (max-width:767px) {.brz .brz-css-1hc8j29 {padding: 24px;}}
.brz .brz-css-4dqojl {margin: 10px 0px 10px 0px;}
@media (min-width:991px) {.brz .brz-css-4dqojl {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
	.brz .brz-css-4dqojl .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-4dqojl {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-4dqojl {margin: 0;}}
.brz .brz-css-62k2cq:not(.brz-image--hovered) {max-width: 100%;}
.brz .brz-css-62k2cq {height: auto;border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-62k2cq {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-62k2cq .brz-picture:after {border-radius: 0px;}
.brz .brz-css-62k2cq .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
.brz .brz-css-62k2cq .brz-picture {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-62k2cq .brz-picture {filter: none;}
@media (min-width:991px) {.brz .brz-css-62k2cq {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq.brz-image--withHover img.brz-img, .brz .brz-css-62k2cq.brz-image--withHover img.dynamic-image, .brz .brz-css-62k2cq.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-62k2cq {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq.brz-image--withHover img.brz-img, .brz .brz-css-62k2cq.brz-image--withHover img.dynamic-image, .brz .brz-css-62k2cq.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-62k2cq {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-62k2cq.brz-image--withHover img.brz-img, .brz .brz-css-62k2cq.brz-image--withHover img.dynamic-image, .brz .brz-css-62k2cq.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-1da5w5f {border-radius: 12px;}
.brz .brz-css-1da5w5f .brz-picture:after {border-radius: 12px;}
@media (min-width:991px) {.brz .brz-css-1da5w5f:not(.brz-image--hovered) {max-width: 100%;}
	.brz .brz-css-1da5w5f {height: auto;border-radius: 12px;mix-blend-mode: normal;}
	.brz .brz-css-1da5w5f:hover {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-1da5w5f .brz-picture:after {border-radius: 12px;}
	.brz .brz-css-1da5w5f:hover .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
	.brz .brz-css-1da5w5f .brz-picture {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1da5w5f:hover .brz-picture {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1da5w5f:hover {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1da5w5f:hover .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1da5w5f:hover .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1da5w5f.brz-image--withHover img.brz-img, .brz .brz-css-1da5w5f.brz-image--withHover img.dynamic-image, .brz .brz-css-1da5w5f.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-8nyu8v.brz-hover-animation__container {max-width: 100%;}
@media (min-width:991px) {.brz .brz-css-1nt7a0j.brz-hover-animation__container {max-width: 100%;}}
.brz .brz-css-1prllfz {padding-top: 56.2504%;}
.brz .brz-css-1prllfz > .brz-img {position: absolute;width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1prllfz {padding-top: 56.25%;}}
@media (max-width:767px) {.brz .brz-css-1prllfz {padding-top: 56.2513%;}}
.brz .brz-css-1gyf6fj {padding-top: 0;}
.brz .brz-css-1gyf6fj > .brz-img {position: inherit;}
@media (min-width:991px) {.brz .brz-css-1gyf6fj {padding-top: 0;}
	.brz .brz-css-1gyf6fj > .brz-img {position: inherit;width: 100%;}}
.brz .brz-css-1m768ot {width: 885.1px;height: 497.87px;margin-left: -110.65px;margin-top: 0px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1m768ot {width: 938.72px;height: 528.03px;margin-left: -117.36px;}}
@media (max-width:767px) {.brz .brz-css-1m768ot {width: 509.35px;height: 286.51px;margin-left: -63.68px;}}
.brz .brz-css-s6mjaf {width: 100%;height: auto;margin-left: auto;margin-top: auto;}
@media (min-width:991px) {.brz .brz-css-s6mjaf {width: 100%;height: auto;margin-left: auto;margin-top: auto;}}
@media (min-width:991px) {.brz .brz-css-z17p2e {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-z17p2e .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-z17p2e {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-wpk51k {height: 34px;}
@media (min-width:991px) {.brz .brz-css-wpk51k {height: 34px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-wpk51k {height: 25px;}}
@media (max-width:767px) {.brz .brz-css-wpk51k {height: 25px;}}
@media (min-width:991px) {.brz .brz-css-vx1gfn {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-vx1gfn > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-vx1gfn:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-vx1gfn > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-vx1gfn:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-vx1gfn:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-vx1gfn > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-vx1gfn:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-vx1gfn > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-vx1gfn:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-vx1gfn > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-vx1gfn:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-vx1gfn:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-vx1gfn {min-height: auto;display: flex;}
	.brz .brz-css-vx1gfn:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-vx1gfn:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-vx1gfn:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-vx1gfn:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-vx1gfn:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-tiodmh {padding: 0;max-width: 100%;}}
.brz .brz-css-oa95ur {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-oa95ur > .brz-bg {margin: 0;}
.brz .brz-css-oa95ur > .brz-bg {border-width: 0px 1px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color7),.3);}
@media (min-width:991px) {.brz .brz-css-oa95ur {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-oa95ur .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-oa95ur > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-oa95ur:hover > .brz-bg {border-width: 0px 1px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color7),.3);box-shadow: none;}
	.brz .brz-css-oa95ur > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-oa95ur:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-oa95ur:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-oa95ur > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-oa95ur:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-oa95ur > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-oa95ur:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-oa95ur > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-oa95ur:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-oa95ur:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oa95ur:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-oa95ur:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-jw81xq {margin: 0;border-width: 0px 1px 0px 0px;border-style: solid;border-color: transparent;padding: 0px 15px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-jw81xq {z-index: auto;margin: 0;border-width: 0px 1px 0px 0px;border-style: solid;border-color: transparent;padding: 0px 15px 0px 0px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-jw81xq:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-jw81xq {padding: 0px 10px 0px 0px;}}
.brz .brz-css-9172he {margin: 0;}
@media (min-width:991px) {.brz .brz-css-9172he {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-9172he .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-9172he {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1q94uue .brz-icon__container {margin-left: auto;margin-right: 5px;}
@media (min-width:991px) {.brz .brz-css-1q94uue {flex-direction: row;}
	.brz .brz-css-1q94uue .brz-icon__container {margin-left: auto;margin-right: 5px;align-items: flex-start;}}
.brz .brz-css-mp2kc5 {font-size: 17px;}
@media (min-width:991px) {.brz .brz-css-mp2kc5 {font-size: 17px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-mp2kc5:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-mp2kc5:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-mp2kc5:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-ni65aj {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-hvV0r {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading6fontfamily,initial) !important;font-size: var(--brz-heading6fontsize,initial);line-height: var(--brz-heading6lineheight,initial);font-weight: var(--brz-heading6fontweight,initial);font-weight: var(--brz-heading6bold,initial);letter-spacing: var(--brz-heading6letterspacing,initial);font-variation-settings: var(--brz-heading6fontvariation,initial);font-style: var(--brz-heading6italic,initial);text-decoration: var(--brz-heading6textdecoration,initial) !important;text-transform: var(--brz-heading6texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-hvV0r {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading6fontfamily,initial) !important;font-size: var(--brz-heading6fontsize,initial);line-height: var(--brz-heading6lineheight,initial);font-weight: var(--brz-heading6fontweight,initial);font-weight: var(--brz-heading6bold,initial);letter-spacing: var(--brz-heading6letterspacing,initial);font-variation-settings: var(--brz-heading6fontvariation,initial);font-style: var(--brz-heading6italic,initial);text-decoration: var(--brz-heading6textdecoration,initial) !important;text-transform: var(--brz-heading6texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-hvV0r {font-size: 13px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
@media (max-width:767px) {.brz .brz-css-hvV0r {font-size: 13px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
@media (min-width:991px) {.brz .brz-css-box8a8 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-box8a8 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-box8a8 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-t8nrzi {height: 10px;}
@media (min-width:991px) {.brz .brz-css-t8nrzi {height: 10px;}}
.brz .brz-css-mhx9ru {margin: 0;}
@media (min-width:991px) {.brz .brz-css-mhx9ru {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-mhx9ru .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-mhx9ru {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-fdqqzl {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-uJQps {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading4fontfamily,initial) !important;font-size: var(--brz-heading4fontsize,initial);line-height: var(--brz-heading4lineheight,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-uJQps {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading4fontfamily,initial) !important;font-size: var(--brz-heading4fontsize,initial);line-height: var(--brz-heading4lineheight,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-uJQps {font-size: var(--brz-heading4tabletfontsize,initial);line-height: var(--brz-heading4tabletlineheight,initial);font-weight: var(--brz-heading4tabletfontweight,initial);font-weight: var(--brz-heading4tabletbold,initial);letter-spacing: var(--brz-heading4tabletletterspacing,initial);font-variation-settings: var(--brz-heading4tabletfontvariation,initial);font-style: var(--brz-heading4tabletitalic,initial);text-decoration: var(--brz-heading4tablettextdecoration,initial) !important;text-transform: var(--brz-heading4tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-uJQps {font-size: var(--brz-heading4mobilefontsize,initial);line-height: var(--brz-heading4mobilelineheight,initial);font-weight: var(--brz-heading4mobilefontweight,initial);font-weight: var(--brz-heading4mobilebold,initial);letter-spacing: var(--brz-heading4mobileletterspacing,initial);font-variation-settings: var(--brz-heading4mobilefontvariation,initial);font-style: var(--brz-heading4mobileitalic,initial);text-decoration: var(--brz-heading4mobiletextdecoration,initial) !important;text-transform: var(--brz-heading4mobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-11uhh8p {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-11uhh8p .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-11uhh8p {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1a2bbvn {height: 10px;}
@media (min-width:991px) {.brz .brz-css-1a2bbvn {height: 10px;}}
.brz .brz-css-u8ge0l {margin: 0;}
@media (min-width:991px) {.brz .brz-css-u8ge0l {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-u8ge0l .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-u8ge0l {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-wzl5qk {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-sGcTv {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-sGcTv {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sGcTv {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-sGcTv {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-tdj4vu {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-tdj4vu .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-tdj4vu {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-tdj4vu {display: none;}}
.brz .brz-css-1jm4twr {height: 18px;}
@media (min-width:991px) {.brz .brz-css-1jm4twr {height: 18px;}}
@media (min-width:991px) {.brz .brz-css-3pcm9i {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-3pcm9i .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-3pcm9i {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-3pcm9i {display: none;}}
.brz .brz-css-11wro6i {height: 30px;}
@media (min-width:991px) {.brz .brz-css-11wro6i {height: 30px;}}
@media (min-width:991px) {.brz .brz-css-178od83 {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-178od83 > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-178od83:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-178od83 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-178od83:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-178od83:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-178od83 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-178od83:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-178od83 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-178od83:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-178od83 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-178od83:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-178od83:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-178od83 {min-height: auto;display: flex;}
	.brz .brz-css-178od83:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-178od83:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-178od83:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-178od83:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-178od83:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-1bbj3y6 {padding: 0;max-width: 100%;}}
.brz .brz-css-1arm5on {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-1arm5on > .brz-bg {margin: 0;border-radius: 12px;}
.brz .brz-css-1arm5on > .brz-bg {box-shadow: 0px 0px 60px 10px rgba(0,0,0,.07);}
.brz .brz-css-1arm5on > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);}
@media (min-width:991px) {.brz .brz-css-1arm5on {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-1arm5on .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-1arm5on > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 12px;}
	.brz .brz-css-1arm5on:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: 0px 0px 60px 0px rgba(var(--brz-global-color7),.22);}
	.brz .brz-css-1arm5on > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1arm5on:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1arm5on:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1arm5on > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1arm5on:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);background-image: none;}
	.brz .brz-css-1arm5on > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1arm5on:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1arm5on > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1arm5on:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1arm5on:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1arm5on:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1arm5on:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1arm5on > .brz-bg {margin: 16px 0px 16px 0px;}}
.brz .brz-css-505i9y {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}
@media (min-width:991px) {.brz .brz-css-505i9y {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}}
.brz .brz-css-3q6k7k {margin: 0;padding: 40px;}
@media (min-width:991px) {.brz .brz-css-3q6k7k {z-index: auto;margin: 0;border: 0px solid transparent;padding: 40px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-3q6k7k:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-3q6k7k {padding: 32px;}}
@media (max-width:767px) {.brz .brz-css-3q6k7k {margin: 16px 0px 16px 0px;padding: 24px;}}
.brz .brz-css-1578od2 {margin: 10px 0px 10px 0px;}
@media (min-width:991px) {.brz .brz-css-1578od2 {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
	.brz .brz-css-1578od2 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1578od2 {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1578od2 {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-1578od2 {margin: 0;}}
.brz .brz-css-9q35a3:not(.brz-image--hovered) {max-width: 100%;}
.brz .brz-css-9q35a3 {height: auto;border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-9q35a3 {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-9q35a3 .brz-picture:after {border-radius: 0px;}
.brz .brz-css-9q35a3 .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
.brz .brz-css-9q35a3 .brz-picture {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-9q35a3 .brz-picture {filter: none;}
@media (min-width:991px) {.brz .brz-css-9q35a3 {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3 .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3 .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3.brz-image--withHover img.brz-img, .brz .brz-css-9q35a3.brz-image--withHover img.dynamic-image, .brz .brz-css-9q35a3.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-9q35a3 {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3 .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3 .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3.brz-image--withHover img.brz-img, .brz .brz-css-9q35a3.brz-image--withHover img.dynamic-image, .brz .brz-css-9q35a3.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-9q35a3 {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3 .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3 .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-9q35a3.brz-image--withHover img.brz-img, .brz .brz-css-9q35a3.brz-image--withHover img.dynamic-image, .brz .brz-css-9q35a3.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-jbtve9 {border-radius: 12px;}
.brz .brz-css-jbtve9 .brz-picture:after {border-radius: 12px;}
@media (min-width:991px) {.brz .brz-css-jbtve9:not(.brz-image--hovered) {max-width: 100%;}
	.brz .brz-css-jbtve9 {height: auto;border-radius: 12px;mix-blend-mode: normal;}
	.brz .brz-css-jbtve9:hover {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-jbtve9 .brz-picture:after {border-radius: 12px;}
	.brz .brz-css-jbtve9:hover .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
	.brz .brz-css-jbtve9 .brz-picture {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-jbtve9:hover .brz-picture {filter: none;}}
@media (min-width:991px) {.brz .brz-css-jbtve9:hover {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbtve9:hover .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbtve9:hover .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jbtve9.brz-image--withHover img.brz-img, .brz .brz-css-jbtve9.brz-image--withHover img.dynamic-image, .brz .brz-css-jbtve9.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-133ph5x.brz-hover-animation__container {max-width: 100%;}
@media (min-width:991px) {.brz .brz-css-b602we.brz-hover-animation__container {max-width: 100%;}}
.brz .brz-css-1se2trw {padding-top: 66.7505%;}
.brz .brz-css-1se2trw > .brz-img {position: absolute;width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1se2trw {padding-top: 66.75%;}}
@media (max-width:767px) {.brz .brz-css-1se2trw {padding-top: 66.7513%;}}
.brz .brz-css-evx8cc {padding-top: 0;}
.brz .brz-css-evx8cc > .brz-img {position: inherit;}
@media (min-width:991px) {.brz .brz-css-evx8cc {padding-top: 0;}
	.brz .brz-css-evx8cc > .brz-img {position: inherit;width: 100%;}}
.brz .brz-css-rqgg1m {width: 745.84px;height: 497.85px;margin-left: -41.02px;margin-top: 0px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-rqgg1m {width: 791.01px;height: 528px;margin-left: -43.5px;}}
@media (max-width:767px) {.brz .brz-css-rqgg1m {width: 429.21px;height: 286.5px;margin-left: -23.6px;}}
.brz .brz-css-1hmzute {width: 100%;height: auto;margin-left: auto;margin-top: auto;}
@media (min-width:991px) {.brz .brz-css-1hmzute {width: 100%;height: auto;margin-left: auto;margin-top: auto;}}
@media (min-width:991px) {.brz .brz-css-17njfa {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-17njfa .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-17njfa {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1e1f4n2 {height: 34px;}
@media (min-width:991px) {.brz .brz-css-1e1f4n2 {height: 34px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1e1f4n2 {height: 25px;}}
@media (max-width:767px) {.brz .brz-css-1e1f4n2 {height: 25px;}}
@media (min-width:991px) {.brz .brz-css-16i5hfg {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-16i5hfg > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-16i5hfg:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-16i5hfg > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-16i5hfg:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-16i5hfg:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-16i5hfg > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-16i5hfg:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-16i5hfg > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-16i5hfg:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-16i5hfg > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-16i5hfg:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-16i5hfg:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-16i5hfg {min-height: auto;display: flex;}
	.brz .brz-css-16i5hfg:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16i5hfg:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16i5hfg:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16i5hfg:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-16i5hfg:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-1dauug0 {padding: 0;max-width: 100%;}}
.brz .brz-css-xfh61 {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-xfh61 > .brz-bg {margin: 0;}
.brz .brz-css-xfh61 > .brz-bg {border-width: 0px 1px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color7),.3);}
@media (min-width:991px) {.brz .brz-css-xfh61 {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-xfh61 .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-xfh61 > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-xfh61:hover > .brz-bg {border-width: 0px 1px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color7),.3);box-shadow: none;}
	.brz .brz-css-xfh61 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-xfh61:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-xfh61:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-xfh61 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-xfh61:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-xfh61 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-xfh61:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-xfh61 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-xfh61:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-xfh61:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-xfh61:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-xfh61:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-ho80ro {margin: 0;border-width: 0px 1px 0px 0px;border-style: solid;border-color: transparent;padding: 0px 15px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-ho80ro {z-index: auto;margin: 0;border-width: 0px 1px 0px 0px;border-style: solid;border-color: transparent;padding: 0px 15px 0px 0px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-ho80ro:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-ho80ro {padding: 0px 10px 0px 0px;}}
.brz .brz-css-zo0y1d {margin: 0;}
@media (min-width:991px) {.brz .brz-css-zo0y1d {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-zo0y1d .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-zo0y1d {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1vaqlfe .brz-icon__container {margin-left: auto;margin-right: 5px;}
@media (min-width:991px) {.brz .brz-css-1vaqlfe {flex-direction: row;}
	.brz .brz-css-1vaqlfe .brz-icon__container {margin-left: auto;margin-right: 5px;align-items: flex-start;}}
.brz .brz-css-lx14r1 {font-size: 17px;}
@media (min-width:991px) {.brz .brz-css-lx14r1 {font-size: 17px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-lx14r1:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-lx14r1:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-lx14r1:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-dkur6m {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-lM3aQ {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading6fontfamily,initial) !important;font-size: var(--brz-heading6fontsize,initial);line-height: var(--brz-heading6lineheight,initial);font-weight: var(--brz-heading6fontweight,initial);font-weight: var(--brz-heading6bold,initial);letter-spacing: var(--brz-heading6letterspacing,initial);font-variation-settings: var(--brz-heading6fontvariation,initial);font-style: var(--brz-heading6italic,initial);text-decoration: var(--brz-heading6textdecoration,initial) !important;text-transform: var(--brz-heading6texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-lM3aQ {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading6fontfamily,initial) !important;font-size: var(--brz-heading6fontsize,initial);line-height: var(--brz-heading6lineheight,initial);font-weight: var(--brz-heading6fontweight,initial);font-weight: var(--brz-heading6bold,initial);letter-spacing: var(--brz-heading6letterspacing,initial);font-variation-settings: var(--brz-heading6fontvariation,initial);font-style: var(--brz-heading6italic,initial);text-decoration: var(--brz-heading6textdecoration,initial) !important;text-transform: var(--brz-heading6texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-lM3aQ {font-size: 13px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
@media (max-width:767px) {.brz .brz-css-lM3aQ {font-size: 13px;line-height: 1.5;font-weight: 400;letter-spacing: 0px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
@media (min-width:991px) {.brz .brz-css-rhzr3n {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-rhzr3n .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-rhzr3n {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1ypxozs {height: 10px;}
@media (min-width:991px) {.brz .brz-css-1ypxozs {height: 10px;}}
.brz .brz-css-zd5emd {margin: 0;}
@media (min-width:991px) {.brz .brz-css-zd5emd {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-zd5emd .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-zd5emd {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-zr3nij {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-qBZrA {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading4fontfamily,initial) !important;font-size: var(--brz-heading4fontsize,initial);line-height: var(--brz-heading4lineheight,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-qBZrA {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading4fontfamily,initial) !important;font-size: var(--brz-heading4fontsize,initial);line-height: var(--brz-heading4lineheight,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-qBZrA {font-size: var(--brz-heading4tabletfontsize,initial);line-height: var(--brz-heading4tabletlineheight,initial);font-weight: var(--brz-heading4tabletfontweight,initial);font-weight: var(--brz-heading4tabletbold,initial);letter-spacing: var(--brz-heading4tabletletterspacing,initial);font-variation-settings: var(--brz-heading4tabletfontvariation,initial);font-style: var(--brz-heading4tabletitalic,initial);text-decoration: var(--brz-heading4tablettextdecoration,initial) !important;text-transform: var(--brz-heading4tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-qBZrA {font-size: var(--brz-heading4mobilefontsize,initial);line-height: var(--brz-heading4mobilelineheight,initial);font-weight: var(--brz-heading4mobilefontweight,initial);font-weight: var(--brz-heading4mobilebold,initial);letter-spacing: var(--brz-heading4mobileletterspacing,initial);font-variation-settings: var(--brz-heading4mobilefontvariation,initial);font-style: var(--brz-heading4mobileitalic,initial);text-decoration: var(--brz-heading4mobiletextdecoration,initial) !important;text-transform: var(--brz-heading4mobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-3uvscw {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-3uvscw .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-3uvscw {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-3uvscw {display: none;}}
.brz .brz-css-9jr3en {height: 10px;}
@media (min-width:991px) {.brz .brz-css-9jr3en {height: 10px;}}
@media (min-width:991px) {.brz .brz-css-1e3kb35 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1e3kb35 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1e3kb35 {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1e3kb35 {display: none;}}
@media (max-width:767px) {.brz .brz-css-1e3kb35 {display: none;}}
.brz .brz-css-1f7fn3g {height: 18px;}
@media (min-width:991px) {.brz .brz-css-1f7fn3g {height: 18px;}}
.brz .brz-css-438ujj {flex: 1 1 35.4%;max-width: 35.4%;}
.brz .brz-css-438ujj > .brz-bg {margin: 0px 0px 0px 12px;}
@media (min-width:991px) {.brz .brz-css-438ujj {z-index: auto;flex: 1 1 35.4%;max-width: 35.4%;justify-content: flex-start;}
	.brz .brz-css-438ujj .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-438ujj > .brz-bg {margin: 0px 0px 0px 12px;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-438ujj:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-438ujj > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-438ujj:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-438ujj:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-438ujj > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-438ujj:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-438ujj > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-438ujj:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-438ujj > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-438ujj:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-438ujj:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-438ujj:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-438ujj:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-438ujj {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-438ujj > .brz-bg {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-438ujj {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-438ujj > .brz-bg {margin: 24px 0px 8px 0px;}}
.brz .brz-css-nsyg99 {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}
@media (min-width:991px) {.brz .brz-css-nsyg99 {animation-name: fadeInRight;animation-duration: 2000ms;animation-delay: 0ms;animation-iteration-count: unset;}}
.brz .brz-css-1tt9xs1 {margin: 0px 0px 0px 12px;padding: 0;}
@media (min-width:991px) {.brz .brz-css-1tt9xs1 {z-index: auto;margin: 0px 0px 0px 12px;border: 0px solid transparent;padding: 0;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1tt9xs1:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tt9xs1 {margin: 0;padding: 40px 64px 32px 64px;}}
@media (max-width:767px) {.brz .brz-css-1tt9xs1 {margin: 24px 0px 8px 0px;}}
@media (min-width:991px) {.brz .brz-css-1hhnxy9 {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-1hhnxy9 > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1hhnxy9 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1hhnxy9 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1hhnxy9 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1hhnxy9 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1hhnxy9:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1hhnxy9 {min-height: auto;display: flex;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1hhnxy9:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1hhnxy9:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-e8rkn7 {padding: 0;max-width: 100%;}}
.brz .brz-css-14b2gl5 {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-14b2gl5 > .brz-bg {border-radius: 12px;}
.brz .brz-css-14b2gl5 > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-14b2gl5 {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-14b2gl5 .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-14b2gl5 > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 12px;}
	.brz .brz-css-14b2gl5:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: 0px 0px 60px 0px rgba(var(--brz-global-color7),.14);}
	.brz .brz-css-14b2gl5 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-14b2gl5:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-14b2gl5:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-14b2gl5 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-14b2gl5:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
	.brz .brz-css-14b2gl5 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-14b2gl5:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-14b2gl5 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-14b2gl5:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-14b2gl5:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-14b2gl5:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-14b2gl5:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-178zsb6 {padding: 40px;}
@media (min-width:991px) {.brz .brz-css-178zsb6 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 40px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-178zsb6:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-178zsb6 {padding: 25px;}}
@media (max-width:767px) {.brz .brz-css-178zsb6 {padding: 25px;}}
@media (min-width:991px) {.brz .brz-css-hbh2p3 {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
	.brz .brz-css-hbh2p3 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-hbh2p3 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-l7g90s:not(.brz-image--hovered) {max-width: 100%;}
.brz .brz-css-l7g90s {height: auto;border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-l7g90s {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-l7g90s .brz-picture:after {border-radius: 0px;}
.brz .brz-css-l7g90s .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
.brz .brz-css-l7g90s .brz-picture {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-l7g90s .brz-picture {filter: none;}
@media (min-width:991px) {.brz .brz-css-l7g90s {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s.brz-image--withHover img.brz-img, .brz .brz-css-l7g90s.brz-image--withHover img.dynamic-image, .brz .brz-css-l7g90s.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-l7g90s:not(.brz-image--hovered) {max-width: 76%;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-l7g90s {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s.brz-image--withHover img.brz-img, .brz .brz-css-l7g90s.brz-image--withHover img.dynamic-image, .brz .brz-css-l7g90s.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-l7g90s {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-l7g90s.brz-image--withHover img.brz-img, .brz .brz-css-l7g90s.brz-image--withHover img.dynamic-image, .brz .brz-css-l7g90s.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-b7hy5k {border-radius: 12px;}
.brz .brz-css-b7hy5k .brz-picture:after {border-radius: 12px;}
@media (min-width:991px) {.brz .brz-css-b7hy5k:not(.brz-image--hovered) {max-width: 100%;}
	.brz .brz-css-b7hy5k {height: auto;border-radius: 12px;mix-blend-mode: normal;}
	.brz .brz-css-b7hy5k:hover {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-b7hy5k .brz-picture:after {border-radius: 12px;}
	.brz .brz-css-b7hy5k:hover .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
	.brz .brz-css-b7hy5k .brz-picture {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-b7hy5k:hover .brz-picture {filter: none;}}
@media (min-width:991px) {.brz .brz-css-b7hy5k:hover {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-b7hy5k:hover .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-b7hy5k:hover .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-b7hy5k.brz-image--withHover img.brz-img, .brz .brz-css-b7hy5k.brz-image--withHover img.dynamic-image, .brz .brz-css-b7hy5k.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-1977yx2.brz-hover-animation__container {max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1977yx2.brz-hover-animation__container {max-width: 76%;}}
@media (min-width:991px) {.brz .brz-css-1xl0t1o.brz-hover-animation__container {max-width: 100%;}}
.brz .brz-css-14b2s53 {padding-top: 28.7989%;}
.brz .brz-css-14b2s53 > .brz-img {position: absolute;width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-14b2s53 {padding-top: 21.8889%;}}
@media (max-width:767px) {.brz .brz-css-14b2s53 {padding-top: 28.8%;}}
.brz .brz-css-1xsdico {padding-top: 0;}
.brz .brz-css-1xsdico > .brz-img {position: inherit;}
@media (min-width:991px) {.brz .brz-css-1xsdico {padding-top: 0;}
	.brz .brz-css-1xsdico > .brz-img {position: inherit;width: 100%;}}
.brz .brz-css-uzda02 {width: 839.06px;height: 241.65px;margin-left: -258.43px;margin-top: 0px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-uzda02 {width: 1536.49px;height: 442.51px;margin-left: -473.24px;}}
@media (max-width:767px) {.brz .brz-css-uzda02 {width: 989.58px;height: 285px;margin-left: -304.79px;}}
.brz .brz-css-1av7aal {width: 100%;height: auto;margin-left: auto;margin-top: auto;}
@media (min-width:991px) {.brz .brz-css-1av7aal {width: 100%;height: auto;margin-left: auto;margin-top: auto;}}
@media (min-width:991px) {.brz .brz-css-1xrohhk {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1xrohhk .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1xrohhk {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-oanq0w {height: 24px;}
@media (min-width:991px) {.brz .brz-css-oanq0w {height: 24px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-oanq0w {height: 25px;}}
.brz .brz-css-1a6mrug {z-index: auto;position: relative;margin: 10px 0px 10px 0px;justify-content: center;padding: 0;gap: 20px 10px;}
@media (min-width:991px) {.brz .brz-css-1a6mrug {position: relative;}
	.brz .brz-css-1a6mrug {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1a6mrug {position: relative;}
	.brz .brz-css-1a6mrug {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1a6mrug {position: relative;}
	.brz .brz-css-1a6mrug {display: flex;}}
.brz .brz-css-1wzokml {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1wzokml {z-index: auto;position: relative;margin: 0;justify-content: center;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1wzokml {position: relative;}
	.brz .brz-css-1wzokml:hover {display: flex;}}
.brz .brz-css-hp8me6 {justify-content: center;padding: 0;gap: 20px 10px;}
@media (min-width:991px) {.brz .brz-css-pfnvhe {justify-content: center;padding: 0;gap: 20px 10px;}}
.brz .brz-css-1qvgjon.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1qvgjon.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1qvgjon.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1qvgjon.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
@media (min-width:991px) {.brz .brz-css-1qvgjon.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1qvgjon.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1qvgjon.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1qvgjon.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1qvgjon.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1qvgjon.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1qvgjon.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1qvgjon.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1qvgjon.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1qvgjon.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:767px) {.brz .brz-css-1qvgjon.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1qvgjon.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1qvgjon.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1qvgjon.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1qvgjon.brz-back-pulse:before {animation-duration: .6s;}}
.brz .brz-css-4gtoyy.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-4gtoyy.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-4gtoyy.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-4gtoyy.brz-btn--hover-in {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-4gtoyy.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-4gtoyy.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-4gtoyy.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-4gtoyy.brz-btn--hover-in {background-color: rgba(var(--brz-global-color8),1);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-4gtoyy.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-4gtoyy.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-4gtoyy.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-4gtoyy.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-4gtoyy.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1qq0lr1.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 0;padding: 14px 42px 14px 42px;padding: 14px 42px;flex-flow: row-reverse nowrap;}
.brz .brz-css-1qq0lr1.brz-btn {display: flex;color: rgba(var(--brz-global-color8),1);border: 2px solid rgba(var(--brz-global-color3),1);box-shadow: none;}
.brz .brz-css-1qq0lr1.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1qq0lr1.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-1qq0lr1.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1qq0lr1:after {height: unset;}
.brz .brz-css-1qq0lr1 .brz-btn--story-container {border: 2px solid rgba(var(--brz-global-color3),1);flex-flow: row-reverse nowrap;border-radius: 0;}
.brz .brz-css-1qq0lr1 .brz-btn--story-container:after {height: unset;}
@media (min-width:991px) {.brz .brz-css-1qq0lr1.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (min-width:991px) {.brz .brz-css-1qq0lr1.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-1qq0lr1.brz-btn.brz-btn-submit:hover {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1qq0lr1.brz-btn {font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);font-size: var(--brz-buttontabletfontsize,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1qq0lr1.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-1qq0lr1.brz-btn {font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);font-size: var(--brz-buttonmobilefontsize,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:767px) {.brz .brz-css-1qq0lr1.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1qq0lr1 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-1s6287f.brz-btn {border-radius: 12px;padding: 19px 50px 19px 50px;padding: 19px 50px;}
.brz .brz-css-1s6287f.brz-btn {border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1s6287f.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1s6287f.brz-btn.brz-btn-submit {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1s6287f .brz-btn--story-container {border: 0px solid rgba(35,157,219,0);border-radius: 12px;}
@media (min-width:991px) {.brz .brz-css-1s6287f.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 12px;padding: 19px 50px 19px 50px;padding: 19px 50px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1s6287f.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(255,255,255,.8);box-shadow: none;}
	.brz .brz-css-1s6287f.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color8),1);background-image: none;}
	.brz .brz-css-1s6287f.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1s6287f.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(var(--brz-global-color8),1);background-image: none;}
	.brz .brz-css-1s6287f:after {height: unset;}
	.brz .brz-css-1s6287f .brz-btn--story-container {border: 0px solid rgba(255,255,255,.8);flex-flow: row-reverse nowrap;border-radius: 12px;}
	.brz .brz-css-1s6287f .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1s6287f.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1s6287f.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1s6287f.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1s6287f .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1s6287f.brz-btn {border-radius: 0px;padding: 19px 44px 19px 44px;padding: 19px 44px;}
	.brz .brz-css-1s6287f .brz-btn--story-container {border-radius: 0px;}}
@media (max-width:767px) {.brz .brz-css-1s6287f.brz-btn {border-radius: 0px;padding: 14px 42px 14px 42px;padding: 14px 42px;}
	.brz .brz-css-1s6287f .brz-btn--story-container {border-radius: 0px;}}
@media (min-width:991px) {.brz .brz-css-9o72yj {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-9o72yj .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-9o72yj {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-9o72yj {display: none;}}
@media (max-width:767px) {.brz .brz-css-9o72yj {display: none;}}
.brz .brz-css-kz09d7 {height: 30px;}
@media (min-width:991px) {.brz .brz-css-kz09d7 {height: 30px;}}
@media (min-width:991px) {.brz .brz-css-1ijr2n9 {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-1ijr2n9 > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1ijr2n9 > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1ijr2n9 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1ijr2n9 > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1ijr2n9 > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1ijr2n9:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1ijr2n9 {min-height: auto;display: flex;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ijr2n9:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1ijr2n9:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-11u2wkc {padding: 0;max-width: 100%;}}
.brz .brz-css-2m4d3p {flex: 1 1 100%;max-width: 100%;}
.brz .brz-css-2m4d3p > .brz-bg {border-radius: 12px;}
.brz .brz-css-2m4d3p > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);}
@media (min-width:991px) {.brz .brz-css-2m4d3p {z-index: auto;flex: 1 1 100%;max-width: 100%;justify-content: flex-start;}
	.brz .brz-css-2m4d3p .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-2m4d3p > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 12px;}
	.brz .brz-css-2m4d3p:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: 0px 0px 60px 0px rgba(var(--brz-global-color7),.14);}
	.brz .brz-css-2m4d3p > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-2m4d3p:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-2m4d3p:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-2m4d3p > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-2m4d3p:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);background-image: none;}
	.brz .brz-css-2m4d3p > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-2m4d3p:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-2m4d3p > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-2m4d3p:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-2m4d3p:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2m4d3p:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-2m4d3p:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-2m4d3p > * {display: none;}
	.brz .brz-css-2m4d3p > .brz-column__items {display: none;}}
@media (max-width:767px) {.brz .brz-css-2m4d3p > * {display: none;}
	.brz .brz-css-2m4d3p > .brz-column__items {display: none;}}
.brz .brz-css-1p1v8an {padding: 40px;}
@media (min-width:991px) {.brz .brz-css-1p1v8an {z-index: auto;margin: 0;border: 0px solid transparent;padding: 40px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1p1v8an:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1p1v8an {padding: 25px;}}
@media (max-width:767px) {.brz .brz-css-1p1v8an {padding: 25px;}}
.brz .brz-css-1gsjv57 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1gsjv57 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1gsjv57 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1gsjv57 {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-1dk8ecp {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-e5FrC {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading4fontfamily,initial) !important;font-size: var(--brz-heading4fontsize,initial);line-height: var(--brz-heading4lineheight,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-e5FrC {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-heading4fontfamily,initial) !important;font-size: var(--brz-heading4fontsize,initial);line-height: var(--brz-heading4lineheight,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-e5FrC {font-size: var(--brz-heading4tabletfontsize,initial);line-height: var(--brz-heading4tabletlineheight,initial);font-weight: var(--brz-heading4tabletfontweight,initial);font-weight: var(--brz-heading4tabletbold,initial);letter-spacing: var(--brz-heading4tabletletterspacing,initial);font-variation-settings: var(--brz-heading4tabletfontvariation,initial);font-style: var(--brz-heading4tabletitalic,initial);text-decoration: var(--brz-heading4tablettextdecoration,initial) !important;text-transform: var(--brz-heading4tablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-e5FrC {font-size: var(--brz-heading4mobilefontsize,initial);line-height: var(--brz-heading4mobilelineheight,initial);font-weight: var(--brz-heading4mobilefontweight,initial);font-weight: var(--brz-heading4mobilebold,initial);letter-spacing: var(--brz-heading4mobileletterspacing,initial);font-variation-settings: var(--brz-heading4mobilefontvariation,initial);font-style: var(--brz-heading4mobileitalic,initial);text-decoration: var(--brz-heading4mobiletextdecoration,initial) !important;text-transform: var(--brz-heading4mobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1eu54zn {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1eu54zn .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1eu54zn {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-14jz63z {height: 10px;}
@media (min-width:991px) {.brz .brz-css-14jz63z {height: 10px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-14jz63z {height: 15px;}}
.brz .brz-css-1ji8hx9 {margin: 0;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-1ji8hx9 {z-index: auto;position: relative;margin: 0;justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1ji8hx9 {position: relative;}
	.brz .brz-css-1ji8hx9:hover {display: flex;}}
.brz .brz-css-151mrgw {justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-151mrgw {justify-content: flex-start;padding: 0;gap: 20px 10px;}}
.brz .brz-css-8l4ene {font-size: 16px;margin-inline-start: 10px;margin-inline-end: 0;stroke-width: 1;}
@media (min-width:991px) {.brz .brz-css-w0eyde {font-size: 16px;margin-inline-start: 10px;margin-inline-end: 0;stroke-width: 1;}}
.brz .brz-css-wgtz9l.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-wgtz9l.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-wgtz9l.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-wgtz9l.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-wgtz9l.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-wgtz9l.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-wgtz9l.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-wgtz9l.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-wgtz9l.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-wgtz9l.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-wgtz9l.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-wgtz9l.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-wgtz9l.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-14u4wrw.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssfontweight,initial);font-weight: var(--brz-u9xihr8qxhssbold,initial);font-size: var(--brz-u9xihr8qxhssfontsize,initial);line-height: var(--brz-u9xihr8qxhsslineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssfontvariation,initial);font-style: var(--brz-u9xihr8qxhssitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstexttransform,initial) !important;padding: 10px 0px 10px 0px;padding: 10px 0px;}
.brz .brz-css-14u4wrw.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-14u4wrw.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-14u4wrw.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-14u4wrw.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-14u4wrw .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-14u4wrw.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssfontweight,initial);font-weight: var(--brz-u9xihr8qxhssbold,initial);font-size: var(--brz-u9xihr8qxhssfontsize,initial);line-height: var(--brz-u9xihr8qxhsslineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssfontvariation,initial);font-style: var(--brz-u9xihr8qxhssitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstexttransform,initial) !important;padding: 10px 0px 10px 0px;padding: 10px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-14u4wrw.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-14u4wrw.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-14u4wrw.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-14u4wrw.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-14u4wrw:after {height: unset;}
	.brz .brz-css-14u4wrw .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-14u4wrw .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-14u4wrw.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-14u4wrw.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-14u4wrw.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-14u4wrw .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-14u4wrw.brz-btn {font-weight: var(--brz-u9xihr8qxhsstabletfontweight,initial);font-weight: var(--brz-u9xihr8qxhsstabletbold,initial);font-size: var(--brz-u9xihr8qxhsstabletfontsize,initial);line-height: var(--brz-u9xihr8qxhsstabletlineheight,initial);letter-spacing: var(--brz-u9xihr8qxhsstabletletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhsstabletfontvariation,initial);font-style: var(--brz-u9xihr8qxhsstabletitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstablettextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstablettexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}}
@media (max-width:767px) {.brz .brz-css-14u4wrw.brz-btn {font-weight: 700;font-size: 13px;line-height: 1.5;letter-spacing: -.4px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1rcxp83 {margin: 0;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-1rcxp83 {z-index: auto;position: relative;margin: 0;justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1rcxp83 {position: relative;}
	.brz .brz-css-1rcxp83:hover {display: flex;}}
.brz .brz-css-1gs2v3u {justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-1gs2v3u {justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1mtlihm {font-size: 16px;margin-inline-start: 10px;margin-inline-end: 0;stroke-width: 1;}}
.brz .brz-css-1atgt3r.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1atgt3r.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-1atgt3r.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1atgt3r.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-1atgt3r.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1atgt3r.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-1atgt3r.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1atgt3r.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-1atgt3r.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1atgt3r.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1atgt3r.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1atgt3r.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1atgt3r.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1nr5xej.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssfontweight,initial);font-weight: var(--brz-u9xihr8qxhssbold,initial);font-size: var(--brz-u9xihr8qxhssfontsize,initial);line-height: var(--brz-u9xihr8qxhsslineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssfontvariation,initial);font-style: var(--brz-u9xihr8qxhssitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstexttransform,initial) !important;padding: 10px 0px 10px 0px;padding: 10px 0px;}
.brz .brz-css-1nr5xej.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1nr5xej.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1nr5xej.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1nr5xej.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1nr5xej .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-1nr5xej.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssfontweight,initial);font-weight: var(--brz-u9xihr8qxhssbold,initial);font-size: var(--brz-u9xihr8qxhssfontsize,initial);line-height: var(--brz-u9xihr8qxhsslineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssfontvariation,initial);font-style: var(--brz-u9xihr8qxhssitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstexttransform,initial) !important;padding: 10px 0px 10px 0px;padding: 10px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1nr5xej.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-1nr5xej.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1nr5xej.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1nr5xej.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1nr5xej:after {height: unset;}
	.brz .brz-css-1nr5xej .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1nr5xej .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1nr5xej.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1nr5xej.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1nr5xej.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1nr5xej .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1nr5xej.brz-btn {font-weight: var(--brz-u9xihr8qxhsstabletfontweight,initial);font-weight: var(--brz-u9xihr8qxhsstabletbold,initial);font-size: var(--brz-u9xihr8qxhsstabletfontsize,initial);line-height: var(--brz-u9xihr8qxhsstabletlineheight,initial);letter-spacing: var(--brz-u9xihr8qxhsstabletletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhsstabletfontvariation,initial);font-style: var(--brz-u9xihr8qxhsstabletitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstablettextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstablettexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}}
@media (max-width:767px) {.brz .brz-css-1nr5xej.brz-btn {font-weight: 700;font-size: 13px;line-height: 1.5;letter-spacing: -.4px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-yjdjoj {margin: 0;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-yjdjoj {z-index: auto;position: relative;margin: 0;justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-yjdjoj {position: relative;}
	.brz .brz-css-yjdjoj:hover {display: flex;}}
.brz .brz-css-jr8tls {justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-jr8tls {justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-qumphi {font-size: 16px;margin-inline-start: 10px;margin-inline-end: 0;stroke-width: 1;}}
.brz .brz-css-q3e05q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-q3e05q.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-q3e05q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-q3e05q.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-q3e05q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-q3e05q.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-q3e05q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-q3e05q.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-q3e05q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-q3e05q.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-q3e05q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-q3e05q.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-q3e05q.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-17suvs3.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssfontweight,initial);font-weight: var(--brz-u9xihr8qxhssbold,initial);font-size: var(--brz-u9xihr8qxhssfontsize,initial);line-height: var(--brz-u9xihr8qxhsslineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssfontvariation,initial);font-style: var(--brz-u9xihr8qxhssitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstexttransform,initial) !important;padding: 10px 0px 10px 0px;padding: 10px 0px;}
.brz .brz-css-17suvs3.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-17suvs3.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-17suvs3.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-17suvs3.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-17suvs3 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-17suvs3.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssfontweight,initial);font-weight: var(--brz-u9xihr8qxhssbold,initial);font-size: var(--brz-u9xihr8qxhssfontsize,initial);line-height: var(--brz-u9xihr8qxhsslineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssfontvariation,initial);font-style: var(--brz-u9xihr8qxhssitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstexttransform,initial) !important;padding: 10px 0px 10px 0px;padding: 10px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-17suvs3.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-17suvs3.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-17suvs3.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-17suvs3.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-17suvs3:after {height: unset;}
	.brz .brz-css-17suvs3 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-17suvs3 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-17suvs3.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-17suvs3.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-17suvs3.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-17suvs3 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-17suvs3.brz-btn {font-weight: var(--brz-u9xihr8qxhsstabletfontweight,initial);font-weight: var(--brz-u9xihr8qxhsstabletbold,initial);font-size: var(--brz-u9xihr8qxhsstabletfontsize,initial);line-height: var(--brz-u9xihr8qxhsstabletlineheight,initial);letter-spacing: var(--brz-u9xihr8qxhsstabletletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhsstabletfontvariation,initial);font-style: var(--brz-u9xihr8qxhsstabletitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstablettextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstablettexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}}
@media (max-width:767px) {.brz .brz-css-17suvs3.brz-btn {font-weight: 700;font-size: 13px;line-height: 1.5;letter-spacing: -.4px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1lvtruc {margin: 0;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-1lvtruc {z-index: auto;position: relative;margin: 0;justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1lvtruc {position: relative;}
	.brz .brz-css-1lvtruc:hover {display: flex;}}
.brz .brz-css-e3l6m4 {justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-e3l6m4 {justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1qzfva0 {font-size: 16px;margin-inline-start: 10px;margin-inline-end: 0;stroke-width: 1;}}
.brz .brz-css-j47apk.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-j47apk.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-j47apk.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-j47apk.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-j47apk.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-j47apk.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-j47apk.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-j47apk.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-j47apk.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-j47apk.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-j47apk.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-j47apk.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-j47apk.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-bbd1a0.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssfontweight,initial);font-weight: var(--brz-u9xihr8qxhssbold,initial);font-size: var(--brz-u9xihr8qxhssfontsize,initial);line-height: var(--brz-u9xihr8qxhsslineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssfontvariation,initial);font-style: var(--brz-u9xihr8qxhssitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstexttransform,initial) !important;padding: 10px 0px 10px 0px;padding: 10px 0px;}
.brz .brz-css-bbd1a0.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-bbd1a0.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-bbd1a0.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-bbd1a0.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-bbd1a0 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-bbd1a0.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssfontweight,initial);font-weight: var(--brz-u9xihr8qxhssbold,initial);font-size: var(--brz-u9xihr8qxhssfontsize,initial);line-height: var(--brz-u9xihr8qxhsslineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssfontvariation,initial);font-style: var(--brz-u9xihr8qxhssitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstexttransform,initial) !important;padding: 10px 0px 10px 0px;padding: 10px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-bbd1a0.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-bbd1a0.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-bbd1a0.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-bbd1a0.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-bbd1a0:after {height: unset;}
	.brz .brz-css-bbd1a0 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-bbd1a0 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-bbd1a0.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-bbd1a0.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-bbd1a0.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-bbd1a0 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bbd1a0.brz-btn {font-weight: var(--brz-u9xihr8qxhsstabletfontweight,initial);font-weight: var(--brz-u9xihr8qxhsstabletbold,initial);font-size: var(--brz-u9xihr8qxhsstabletfontsize,initial);line-height: var(--brz-u9xihr8qxhsstabletlineheight,initial);letter-spacing: var(--brz-u9xihr8qxhsstabletletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhsstabletfontvariation,initial);font-style: var(--brz-u9xihr8qxhsstabletitalic,initial);text-decoration: var(--brz-u9xihr8qxhsstablettextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhsstablettexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}}
@media (max-width:767px) {.brz .brz-css-bbd1a0.brz-btn {font-weight: 700;font-size: 13px;line-height: 1.5;letter-spacing: -.4px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
@media (min-width:991px) {.brz .brz-css-sk5zf3 {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sk5zf3 {display: block;}}
@media (max-width:767px) {.brz .brz-css-sk5zf3 {display: block;}}
@media (min-width:991px) {.brz .brz-css-c8fq4o:hover {display: block;}}
.brz .brz-css-1wzn4p1 {padding: 75px 0px 75px 0px;margin: 0;}
.brz .brz-css-1wzn4p1 > .brz-bg {border-radius: 0px;}
.brz .brz-css-1wzn4p1 > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-1wzn4p1 > .brz-bg:after {box-shadow: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-image {-webkit-mask-image: none;mask-image: none;background-size: cover;background-repeat: no-repeat;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-image {background-image: none;filter: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-1wzn4p1 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1wzn4p1 {padding: 50px 15px 50px 15px;}}
@media (max-width:767px) {.brz .brz-css-1wzn4p1 {padding: 25px 15px 25px 15px;}}
.brz .brz-css-1uopts9 {padding: 0;margin: 0px 48px 0px 48px;}
.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-color {background-color: rgba(17,90,47,0);}
@media (min-width:991px) {.brz .brz-css-1uopts9 {padding: 0;margin: 0px 48px 0px 48px;}
	.brz .brz-css-1uopts9 > .brz-bg {border-radius: 0px;}
	.brz .brz-css-1uopts9:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-1uopts9:hover > .brz-bg:after {box-shadow: none;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-image {-webkit-mask-image: none;mask-image: none;background-size: cover;background-repeat: no-repeat;}
	.brz .brz-css-1uopts9:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;}
	.brz .brz-css-1uopts9:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1uopts9:hover > .brz-bg > .brz-bg-color {background-color: rgba(17,90,47,0);background-image: none;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-1uopts9 > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1uopts9 {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-1uopts9 {margin: 0;}}
.brz .brz-css-2gpbzd {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-2gpbzd {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-2gpbzd {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-2gpbzd {max-width: 100%;}}
@media (min-width:991px) {.brz .brz-css-1efqzy4:hover {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1efqzy4 {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-1t66ezt {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-1t66ezt > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-1t66ezt > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-1t66ezt > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1t66ezt {min-height: auto;display: flex;}
	.brz .brz-css-1t66ezt > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1t66ezt {min-height: auto;display: flex;}
	.brz .brz-css-1t66ezt > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-1t66ezt {min-height: auto;display: flex;}
	.brz .brz-css-1t66ezt > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t66ezt > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
.brz .brz-css-1a7ogzm > .brz-bg {border-radius: 16px;}
.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);}
@media (min-width:991px) {.brz .brz-css-1a7ogzm {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-1a7ogzm > .brz-bg {border-radius: 16px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),1);background-image: none;}
	.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1a7ogzm > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1a7ogzm:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1a7ogzm {min-height: auto;display: flex;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1a7ogzm:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1a7ogzm:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1a7ogzm > .brz-bg {border-radius: 0px;}}
@media (max-width:767px) {.brz .brz-css-1a7ogzm > .brz-bg {border-radius: 0px;}}
.brz .brz-css-lt4404 {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-lt4404 {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-lt4404 {padding: 0;}}
.brz .brz-css-18oneyz {padding: 8px 40px 8px 40px;}
@media (min-width:991px) {.brz .brz-css-18oneyz {padding: 8px 40px 8px 40px;max-width: 100%;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-18oneyz {padding: 8px 32px 8px 24px;}}
@media (max-width:767px) {.brz .brz-css-18oneyz {padding: 8px 24px 8px 16px;}}
.brz .brz-css-1csnpdv {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-1csnpdv .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-1csnpdv > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-1csnpdv > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-1csnpdv > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1csnpdv > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1csnpdv {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1csnpdv > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-1csnpdv > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1csnpdv > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-88dicl {flex: 1 1 26%;max-width: 26%;justify-content: center;}
.brz .brz-css-88dicl .brz-columns__scroll-effect {justify-content: center;}
.brz .brz-css-88dicl > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-88dicl {z-index: auto;flex: 1 1 26%;max-width: 26%;justify-content: center;}
	.brz .brz-css-88dicl .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-88dicl > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-88dicl:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-88dicl > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-88dicl > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-88dicl > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-88dicl > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-88dicl:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-88dicl:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-88dicl {flex: 1 1 37.3%;max-width: 37.3%;}}
@media (max-width:767px) {.brz .brz-css-88dicl {flex: 1 1 60%;max-width: 60%;}}
.brz .brz-css-1pzte4r {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-1pzte4r {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1pzte4r {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1pzte4r {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1pzte4r {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-znagm0 {margin: 0;padding: 0;}
@media (min-width:991px) {.brz .brz-css-znagm0 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 0;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-znagm0:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1tjbyob {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-1tjbyob .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-1tjbyob {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tjbyob {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1tjbyob {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1puqgto {margin: 0;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-1puqgto {padding: 0;margin: 0;justify-content: flex-start;position: relative;}
	.brz .brz-css-1puqgto .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1puqgto {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-q31jzm:not(.brz-image--hovered) {max-width: 100%;}
.brz .brz-css-q31jzm {height: auto;border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-q31jzm {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-q31jzm .brz-picture:after {border-radius: 0px;}
.brz .brz-css-q31jzm .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
.brz .brz-css-q31jzm .brz-picture {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-q31jzm .brz-picture {filter: none;}
@media (min-width:991px) {.brz .brz-css-q31jzm {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm.brz-image--withHover img.brz-img, .brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image, .brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-q31jzm {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm.brz-image--withHover img.brz-img, .brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image, .brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (max-width:767px) {.brz .brz-css-q31jzm {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-q31jzm.brz-image--withHover img.brz-img, .brz .brz-css-q31jzm.brz-image--withHover img.dynamic-image, .brz .brz-css-q31jzm.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
@media (min-width:991px) {.brz .brz-css-yp9xav:not(.brz-image--hovered) {max-width: 100%;}
	.brz .brz-css-yp9xav {height: auto;border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-yp9xav:hover {box-shadow: none;border: 0px solid rgba(102,115,141,0);}
	.brz .brz-css-yp9xav .brz-picture:after {border-radius: 0px;}
	.brz .brz-css-yp9xav:hover .brz-picture:after {box-shadow: none;background-color: rgba(255,255,255,0);background-image: none;}
	.brz .brz-css-yp9xav .brz-picture {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-yp9xav:hover .brz-picture {filter: none;}}
@media (min-width:991px) {.brz .brz-css-yp9xav:hover {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-yp9xav:hover .brz-picture:after {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-yp9xav:hover .brz-picture {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-yp9xav.brz-image--withHover img.brz-img, .brz .brz-css-yp9xav.brz-image--withHover img.dynamic-image, .brz .brz-css-yp9xav.brz-image--withHover .brz-img__hover {transition-duration: .5s;}}
.brz .brz-css-h68n6u.brz-hover-animation__container {max-width: 100%;}
@media (min-width:991px) {.brz .brz-css-ypzqsr.brz-hover-animation__container {max-width: 100%;}}
.brz .brz-css-1yykq65 {padding-top: 28.8003%;}
.brz .brz-css-1yykq65 > .brz-img {position: absolute;width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1yykq65 {padding-top: 28.7989%;}}
@media (max-width:767px) {.brz .brz-css-1yykq65 {padding-top: 28.7991%;}}
.brz .brz-css-z4zlrs {padding-top: 0;}
.brz .brz-css-z4zlrs > .brz-img {position: inherit;}
@media (min-width:991px) {.brz .brz-css-z4zlrs {padding-top: 0;}
	.brz .brz-css-z4zlrs > .brz-img {position: inherit;width: 100%;}}
.brz .brz-css-uk9miq {width: 378.89px;height: 109.12px;margin-left: -81.75px;margin-top: 0px;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-uk9miq {width: 299.03px;height: 86.12px;margin-left: -64.52px;}}
@media (max-width:767px) {.brz .brz-css-uk9miq {width: 329.31px;height: 94.84px;margin-left: -71.05px;}}
.brz .brz-css-vw7hrb {width: 100%;height: auto;margin-left: auto;margin-top: auto;}
@media (min-width:991px) {.brz .brz-css-vw7hrb {width: 100%;height: auto;margin-left: auto;margin-top: auto;}}
.brz .brz-css-1t2tmqk {flex: 1 1 67.3%;max-width: 67.3%;justify-content: center;}
.brz .brz-css-1t2tmqk .brz-columns__scroll-effect {justify-content: center;}
.brz .brz-css-1t2tmqk > .brz-bg {margin: 0px 32px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1t2tmqk {z-index: auto;flex: 1 1 67.3%;max-width: 67.3%;justify-content: center;}
	.brz .brz-css-1t2tmqk .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-1t2tmqk > .brz-bg {margin: 0px 32px 0px 0px;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1t2tmqk > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1t2tmqk > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1t2tmqk > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1t2tmqk > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1t2tmqk:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1t2tmqk:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1t2tmqk {flex: 1 1 61.3%;max-width: 61.3%;}
	.brz .brz-css-1t2tmqk > .brz-bg {margin: 0;}}
@media (max-width:767px) {.brz .brz-css-1t2tmqk {flex: 1 1 40%;max-width: 40%;}
	.brz .brz-css-1t2tmqk > .brz-bg {margin: 0;}}
.brz .brz-css-kmg040 {margin: 0px 32px 0px 0px;padding: 0px 0px 0px 16px;}
@media (min-width:991px) {.brz .brz-css-kmg040 {z-index: auto;margin: 0px 32px 0px 0px;border: 0px solid transparent;padding: 0px 0px 0px 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-kmg040:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-kmg040 {margin: 0;padding: 0px 0px 0px 5px;}}
@media (max-width:767px) {.brz .brz-css-kmg040 {margin: 0;padding: 0px 8px 0px 0px;}}
.brz .brz-css-icg1tg {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-icg1tg {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;position: relative;}
	.brz .brz-css-icg1tg .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-icg1tg {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-icg1tg {padding: 0;margin: 5px 0px 5px 0px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-icg1tg {display: none;}}
@media (max-width:767px) {.brz .brz-css-icg1tg {padding: 0;margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-icg1tg {display: none;}}
@media (min-width:991px) {.brz .brz-css-19uzdaw .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-19uzdaw .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-19uzdaw .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-19uzdaw .brz-mm-menu__icon {display: flex;font-size: 18px;}
	.brz .brz-css-19uzdaw .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-19uzdaw .brz-menu {display: none;}}
@media (max-width:767px) {.brz .brz-css-19uzdaw .brz-mm-menu__icon {display: flex;font-size: 18px;}
	.brz .brz-css-19uzdaw .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-19uzdaw .brz-menu {display: none;}}
@media (min-width:991px) {.brz .brz-css-159oqwi .brz-mm-menu__icon {font-size: 18px;}
	.brz .brz-css-159oqwi .brz-mm-menu__icon {color: rgba(51,51,51,1);}}
@media (min-width:991px) {.brz .brz-css-159oqwi .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-159oqwi:hover .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-159oqwi .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-159oqwi .brz-mm-menu__icon {font-size: 38px;}
	.brz .brz-css-159oqwi .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
@media (max-width:767px) {.brz .brz-css-159oqwi .brz-mm-menu__icon {font-size: 32px;}
	.brz .brz-css-159oqwi .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
.brz .brz-css-c39cel .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -5px 0px -5px;}
.brz .brz-css-c39cel .brz-menu__ul {color: rgba(0,0,0,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(0,0,0,1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(0,0,0,1);background-color: rgba(255,255,255,0);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 15px 0 0;}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(0,0,0,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {color: rgba(0,0,0,1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
.brz .brz-css-c39cel .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);border-radius: 0px;}
.brz .brz-css-c39cel .brz-menu__sub-menu {color: rgba(255,255,255,1);background-color: rgba(51,51,51,1);box-shadow: none;}
.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(51,51,51,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(51,51,51,1);}
.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(51,51,51,1);color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {border-color: rgba(255,255,255,1);}
.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}
@media (min-width:991px) {.brz .brz-css-c39cel .brz-menu__ul {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-c39cel .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-c39cel [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-c39cel .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-c39cel .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-c39cel .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-c39cel .brz-mega-menu__dropdown {display: none;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown > .brz-a:after {border-right-style: solid;border-left-style: none;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-menu__sub-menu {position: relative;top: auto;left: auto;transform: translate(0,0);height: 0;overflow: hidden;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item--opened > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}
	.brz .brz-css-c39cel.brz-menu__preview .brz-menu__sub-menu .brz-menu__item > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}}
@media (max-width:767px) {.brz .brz-css-c39cel .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-c39cel .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-c39cel .brz-menu__ul > .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-c39cel .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__item-dropdown .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-c39cel > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-c39cel .brz-mega-menu__dropdown {display: block;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown > .brz-a:after {border-right-style: solid;border-left-style: none;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item-dropdown .brz-menu__sub-menu {position: relative;top: auto;left: auto;transform: translate(0,0);height: 0;overflow: hidden;}
	.brz .brz-css-c39cel .brz-menu__sub-menu .brz-menu__item--opened > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}
	.brz .brz-css-c39cel.brz-menu__preview .brz-menu__sub-menu .brz-menu__item > .brz-menu__sub-menu {height: auto;width: 100%;left: auto;right: auto;}}
.brz .brz-css-1tdvdmo .brz-menu__ul {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);margin: 0px -17.5px 0px -17.5px;}
.brz .brz-css-1tdvdmo .brz-menu__ul {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu {color: rgba(164,248,240,1);background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-a:hover {color: rgba(164,248,240,1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(164,248,240,1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current) > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(164,248,240,1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(var(--brz-global-color3),1);color: rgba(164,248,240,1);}
.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown .brz-a:hover:after {border-color: rgba(164,248,240,1);}
@media (min-width:991px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-size: var(--brz-ugudlcdcxlbqfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -17.5px 0px -17.5px;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__ul {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 15px 0 0;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);border-radius: 0px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu:hover {color: rgba(255,255,255,1);background-color: rgba(var(--brz-global-color3),1);box-shadow: none;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item:hover {background-color: rgba(var(--brz-global-color3),1);color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {border-color: rgba(255,255,255,1);}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}}
@media (min-width:991px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-size: var(--brz-ugudlcdcxlbqfontsize,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__item-dropdown .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1tdvdmo [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-1tdvdmo [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-1tdvdmo > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-1tdvdmo > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-1tdvdmo > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-1tdvdmo .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:767px) {.brz .brz-css-1tdvdmo .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-1tdvdmo .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-1tdvdmo .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}}
.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {color: rgba(255,255,255,1);background-color: #333;}
.brz .brz-css-12dn42o .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-12dn42o .brz-menu__item {color: rgba(255,255,255,1);border-color: rgba(85,85,85,1);}
.brz nav.brz-mm-menu.brz-css-12dn42o {background-color: rgba(51,51,51,.8);}
.brz .brz-css-12dn42o.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
.brz .brz-css-12dn42o .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o .brz-mm-navbar {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {background-color: rgba(51,51,51,.8);}
.brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(51,51,51,.8);}
.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(85,85,85,1);}
.brz .brz-css-12dn42o .brz-mm-listitem {border-color: rgba(85,85,85,1);}
.brz .brz-css-12dn42o  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
.brz .brz-css-12dn42o  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
@media (min-width:991px) {.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-12dn42o .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-12dn42o .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-12dn42o {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-12dn42o .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-12dn42o .brz-menu__item {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-12dn42o .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-12dn42o {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-12dn42o .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttontabletlineheight,initial) * var(--brz-buttontabletfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-12dn42o .brz-mm-navbar .brz-mm-close {transition-duration: .3s;}
	.brz .brz-css-12dn42o .brz-menu__item {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-12dn42o .brz-menu__item {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-12dn42o {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-12dn42o .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-12dn42o .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;border-color: rgba(85,85,85,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonmobilelineheight,initial) * var(--brz-buttonmobilefontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-12dn42o .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-12dn42o .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-1x5bkh9 .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-1x5bkh9 .brz-menu__item {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
.brz nav.brz-mm-menu.brz-css-1x5bkh9 {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;}
.brz .brz-css-1x5bkh9 .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1x5bkh9 .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1x5bkh9 .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1x5bkh9 .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1x5bkh9 .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1x5bkh9 .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1x5bkh9 .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1x5bkh9  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1x5bkh9  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}}
@media (min-width:991px) {.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close:hover {color: rgba(255,255,255,1);background-color: #333;}
	.brz .brz-css-1x5bkh9 .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
	.brz .brz-css-1x5bkh9 .brz-menu__item:hover {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
	.brz nav.brz-mm-menu.brz-css-1x5bkh9 {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
	.brz .brz-css-1x5bkh9:hover .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1x5bkh9 .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1x5bkh9:hover .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1x5bkh9:hover .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1x5bkh9 .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9:hover .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);}
	.brz:hover .brz-css-1x5bkh9 .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1x5bkh9  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}}
@media (min-width:991px) {.brz .brz-css-1x5bkh9 .brz-mm-navbar .brz-mm-close:hover {transition-duration: .3s;}
	.brz .brz-css-1x5bkh9 .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1x5bkh9 .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1x5bkh9 {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9:hover .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1x5bkh9 .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1x5bkh9 .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9:hover .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1x5bkh9:hover .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1x5bkh9:hover .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-family: var(--brz-heading3fontfamily,initial);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-size: var(--brz-heading3tabletfontsize,initial);font-weight: var(--brz-heading3tabletfontweight,initial);font-weight: var(--brz-heading3tabletbold,initial);line-height: var(--brz-heading3tabletlineheight,initial);letter-spacing: var(--brz-heading3tabletletterspacing,initial);font-variation-settings: var(--brz-heading3tabletfontvariation,initial);font-style: var(--brz-heading3tabletitalic,initial);text-decoration: var(--brz-heading3tablettextdecoration,initial) !important;text-transform: var(--brz-heading3tablettexttransform,initial) !important;}
	.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {font-family: var(--brz-heading3fontfamily,initial);font-size: var(--brz-heading3tabletfontsize,initial);font-weight: var(--brz-heading3tabletfontweight,initial);font-weight: var(--brz-heading3tabletbold,initial);line-height: var(--brz-heading3tabletlineheight,initial);letter-spacing: var(--brz-heading3tabletletterspacing,initial);font-variation-settings: var(--brz-heading3tabletfontvariation,initial);font-style: var(--brz-heading3tabletitalic,initial);text-decoration: var(--brz-heading3tablettextdecoration,initial) !important;text-transform: var(--brz-heading3tablettexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading3tabletlineheight,initial) * var(--brz-heading3tabletfontsize,initial) + 15px + 15px);padding-right: 20px;}}
@media (max-width:767px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-family: var(--brz-heading3fontfamily,initial);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}}
@media (max-width:767px) {.brz .brz-css-1x5bkh9 .brz-menu__item {font-size: var(--brz-heading3mobilefontsize,initial);font-weight: var(--brz-heading3mobilefontweight,initial);font-weight: var(--brz-heading3mobilebold,initial);line-height: var(--brz-heading3mobilelineheight,initial);letter-spacing: var(--brz-heading3mobileletterspacing,initial);font-variation-settings: var(--brz-heading3mobilefontvariation,initial);font-style: var(--brz-heading3mobileitalic,initial);text-decoration: var(--brz-heading3mobiletextdecoration,initial) !important;text-transform: var(--brz-heading3mobiletexttransform,initial) !important;}
	.brz .brz-css-1x5bkh9 .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1x5bkh9 .brz-mm-navbar {font-family: var(--brz-heading3fontfamily,initial);font-size: var(--brz-heading3mobilefontsize,initial);font-weight: var(--brz-heading3mobilefontweight,initial);font-weight: var(--brz-heading3mobilebold,initial);line-height: var(--brz-heading3mobilelineheight,initial);letter-spacing: var(--brz-heading3mobileletterspacing,initial);font-variation-settings: var(--brz-heading3mobilefontvariation,initial);font-style: var(--brz-heading3mobileitalic,initial);text-decoration: var(--brz-heading3mobiletextdecoration,initial) !important;text-transform: var(--brz-heading3mobiletexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1x5bkh9.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading3mobilelineheight,initial) * var(--brz-heading3mobilefontsize,initial) + 15px + 15px);padding-right: 20px;}}
.brz .brz-css-19ioiov {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-19ioiov {display: none;}}
@media (min-width:991px) {.brz .brz-css-19ioiov {padding: 8px;margin: 8px 0px 8px 0px;justify-content: flex-end;position: relative;}
	.brz .brz-css-19ioiov .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-19ioiov {display: flex;display: none;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-19ioiov {padding: 0;margin: 5px 0px 5px 0px;}}
@media (max-width:767px) {.brz .brz-css-19ioiov {padding: 0;margin: 10px 0px 10px 0px;}}
@media (min-width:991px) {.brz .brz-css-1mxc0dk .brz-mm-menu__icon {font-size: 18px;}
	.brz .brz-css-1mxc0dk .brz-mm-menu__icon {color: rgba(51,51,51,1);}}
@media (min-width:991px) {.brz .brz-css-1mxc0dk .brz-mm-menu__icon {display: none;font-size: 18px;}
	.brz .brz-css-1mxc0dk:hover .brz-mm-menu__icon {color: rgba(51,51,51,1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1mxc0dk .brz-menu {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1mxc0dk .brz-mm-menu__icon {font-size: 28px;}
	.brz .brz-css-1mxc0dk .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
@media (max-width:767px) {.brz .brz-css-1mxc0dk .brz-mm-menu__icon {font-size: 28px;}
	.brz .brz-css-1mxc0dk .brz-mm-menu__icon {color: rgba(var(--brz-global-color7),1);}}
.brz .brz-css-1dgf3el .brz-menu__ul {font-family: var(--brz-heading4fontfamily,initial);margin: 0px -17.5px 0px -17.5px;}
.brz .brz-css-1dgf3el .brz-menu__ul {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 4px 0 0;}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-size: var(--brz-heading4fontsize,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);line-height: var(--brz-heading4lineheight,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 20px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-family: var(--brz-heading4fontfamily,initial);display: flex;flex-wrap: wrap;justify-content: inherit;align-items: center;max-width: none;margin: 0px -17.5px 0px -17.5px;}
	.brz .brz-css-1dgf3el:hover .brz-menu__ul {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a {flex-flow: row nowrap;padding: 0px 5px 0px 5px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {color: rgba(var(--brz-global-color2),1);background-color: rgba(255,255,255,0);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {margin: 0 4px 0 0;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {border-radius: 0px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item:hover {color: rgba(var(--brz-global-color2),1);background-color: transparent;border: 0px solid rgba(85,85,85,1);}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > a {border-radius: 0px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-family: var(--brz-heading5fontfamily,initial);border-radius: 0px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu:hover {color: rgba(255,255,255,1);background-color: rgba(var(--brz-global-color3),1);box-shadow: none;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item > .brz-a {flex-flow: row nowrap;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu .brz-a:hover {color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-a > .brz-icon-svg {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__item--current .brz-menu__sub-menu {box-shadow: none;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item:hover {background-color: rgba(var(--brz-global-color3),1);color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {border-color: rgba(255,255,255,1);}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item {border-bottom: 1px solid rgba(85,85,85,1);}}
@media (min-width:991px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-size: var(--brz-heading4fontsize,initial);font-weight: var(--brz-heading4fontweight,initial);font-weight: var(--brz-heading4bold,initial);line-height: var(--brz-heading4lineheight,initial);letter-spacing: var(--brz-heading4letterspacing,initial);font-variation-settings: var(--brz-heading4fontvariation,initial);font-style: var(--brz-heading4italic,initial);text-decoration: var(--brz-heading4textdecoration,initial) !important;text-transform: var(--brz-heading4texttransform,initial) !important;}
	.brz .brz-css-1dgf3el:hover .brz-menu__ul {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened > .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--opened:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 20px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current > .brz-a:not(.brz-a:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 17.5px;margin-left: 17.5px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-size: var(--brz-heading5fontsize,initial);font-weight: var(--brz-heading5fontweight,initial);font-weight: var(--brz-heading5bold,initial);line-height: var(--brz-heading5lineheight,initial);letter-spacing: var(--brz-heading5letterspacing,initial);font-variation-settings: var(--brz-heading5fontvariation,initial);font-style: var(--brz-heading5italic,initial);text-decoration: var(--brz-heading5textdecoration,initial) !important;text-transform: var(--brz-heading5texttransform,initial) !important;position: absolute;top: 0;width: 305px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu .brz-a:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__item--current .brz-menu__sub-menu {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item > .brz-a:hover > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu > .brz-menu__item:not(.brz-menu__item.brz-menu__item--current):hover > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el:hover .brz-menu__sub-menu > .brz-menu__item.brz-menu__item--current > .brz-a > .brz-icon-svg.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__item-dropdown .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu .brz-menu__item-dropdown:hover .brz-a:hover:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1dgf3el [data-popper-placement='left-start'] {right: calc(100% + 5px);}
	.brz .brz-css-1dgf3el [data-popper-placement='right-start'] {left: calc(100% + 5px);}
	.brz .brz-css-1dgf3el > .brz-menu__ul > .brz-menu__item-dropdown > .brz-menu__sub-menu {top: calc(100% + 5px);width: 300px;}
	.brz .brz-css-1dgf3el > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='left-start'] {right: 0;}
	.brz .brz-css-1dgf3el > .brz-menu__ul > .brz-menu__item-dropdown > [data-popper-placement='right-start'] {left: 0;}
	.brz .brz-css-1dgf3el .brz-mega-menu__dropdown {display: none;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-size: var(--brz-buttontabletfontsize,initial);font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-family: var(--brz-buttonfontfamily,initial);margin: 0px -5px 0px -5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-family: var(--brz-buttonfontfamily,initial);}}
@media (max-width:767px) {.brz .brz-css-1dgf3el .brz-menu__ul {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item > .brz-a > .brz-icon-svg {font-size: 12px;}
	.brz .brz-css-1dgf3el .brz-menu__ul > .brz-menu__item {padding-top: 0px;padding-bottom: 0px;margin-right: 5px;margin-left: 5px;}
	.brz .brz-css-1dgf3el .brz-menu__sub-menu {font-size: var(--brz-buttonmobilefontsize,initial);font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;}}
.brz .brz-css-1kwae1d .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
.brz .brz-css-1kwae1d .brz-menu__item {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
.brz nav.brz-mm-menu.brz-css-1kwae1d {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;}
.brz .brz-css-1kwae1d .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1kwae1d .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1kwae1d .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1kwae1d .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1kwae1d .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-1kwae1d .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1kwae1d .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}}
@media (min-width:991px) {.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close {font-size: 16px;margin: 0;padding: 10px 15px 10px 10px;}
	.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close:hover {color: rgba(255,255,255,1);background-color: #333;}
	.brz .brz-css-1kwae1d .brz-menu__item {font-family: var(--brz-buttonfontfamily,initial);}
	.brz .brz-css-1kwae1d .brz-menu__item:hover {color: rgba(var(--brz-global-color7),1);border-color: rgba(var(--brz-global-color6),1);}
	.brz nav.brz-mm-menu.brz-css-1kwae1d {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 10px 20px 10px 20px;flex-flow: row nowrap;}
	.brz .brz-css-1kwae1d:hover .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1kwae1d .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1kwae1d .brz-mm-navbar {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1kwae1d:hover .brz-menu__item.brz-mm-listitem_opened {color: rgba(var(--brz-global-color7),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels {background-image: none;}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1kwae1d:hover .brz-mm-panels > .brz-mm-panel {background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1kwae1d .brz-mm-panels > .brz-mm-panel {background-image: none;background-color: rgba(var(--brz-global-color3),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d:hover .brz-mm-listitem {border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active):hover {color: rgba(var(--brz-global-color2),1);}
	.brz:hover .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}}
@media (min-width:991px) {.brz .brz-css-1kwae1d .brz-mm-navbar .brz-mm-close:hover {transition-duration: .3s;}
	.brz .brz-css-1kwae1d .brz-menu__item {font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;}
	.brz .brz-css-1kwae1d .brz-menu__item:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz nav.brz-mm-menu.brz-css-1kwae1d {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d:hover .brz-menu__item:hover > .brz-mm-listitem__text {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-a {justify-content: flex-start;text-align: start;}
	.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {margin: 0 15px 0 0;font-size: 12px;}
	.brz .brz-css-1kwae1d .brz-menu__item:hover .brz-mm-menu__item__icon.brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d .brz-mm-navbar {font-family: var(--brz-buttonfontfamily,initial);font-size: var(--brz-buttonfontsize,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d:hover .brz-menu__item.brz-mm-listitem_opened {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-buttonlineheight,initial) * var(--brz-buttonfontsize,initial) + 10px + 10px);padding-right: 20px;}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-panels, .brz .brz-css-1kwae1d:hover .brz-mm-panels > .brz-mm-panel {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d.brz-mm-menu.brz-mm-menu_theme-dark .brz-mm-navbar.brz-mm-listitem .brz-mm-listitem_opened > .brz-mm-listitem__text:after {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1kwae1d:hover .brz-mm-listitem {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1kwae1d .brz-menu__item {font-family: var(--brz-heading5fontfamily,initial);}
	.brz .brz-css-1kwae1d .brz-menu__item {color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}
	.brz .brz-css-1kwae1d .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d .brz-mm-navbar {color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {color: rgba(108,230,216,1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(var(--brz-global-color8),1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1kwae1d .brz-menu__item {font-size: var(--brz-heading5tabletfontsize,initial);font-weight: var(--brz-heading5tabletfontweight,initial);font-weight: var(--brz-heading5tabletbold,initial);line-height: var(--brz-heading5tabletlineheight,initial);letter-spacing: var(--brz-heading5tabletletterspacing,initial);font-variation-settings: var(--brz-heading5tabletfontvariation,initial);font-style: var(--brz-heading5tabletitalic,initial);text-decoration: var(--brz-heading5tablettextdecoration,initial) !important;text-transform: var(--brz-heading5tablettexttransform,initial) !important;}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {margin: 0 4px 0 0;font-size: 20px;}
	.brz .brz-css-1kwae1d .brz-mm-navbar {font-family: var(--brz-heading5fontfamily,initial);font-size: var(--brz-heading5tabletfontsize,initial);font-weight: var(--brz-heading5tabletfontweight,initial);font-weight: var(--brz-heading5tabletbold,initial);line-height: var(--brz-heading5tabletlineheight,initial);letter-spacing: var(--brz-heading5tabletletterspacing,initial);font-variation-settings: var(--brz-heading5tabletfontvariation,initial);font-style: var(--brz-heading5tabletitalic,initial);text-decoration: var(--brz-heading5tablettextdecoration,initial) !important;text-transform: var(--brz-heading5tablettexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-heading5tabletlineheight,initial) * var(--brz-heading5tabletfontsize,initial) + 15px + 15px);padding-right: 20px;}}
@media (max-width:767px) {.brz .brz-css-1kwae1d .brz-menu__item {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);}
	.brz .brz-css-1kwae1d .brz-menu__item {color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-menu__item .brz-mm-listitem__text {padding: 15px 20px 15px 20px;}
	.brz .brz-css-1kwae1d .brz-menu__item:hover > .brz-mm-listitem__text {color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-mm-menu__item__icon.brz-icon-svg-custom {background-color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d .brz-mm-navbar {color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d .brz-menu__item.brz-mm-listitem_opened {color: rgba(130,247,234,1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(.brz-menu__item.brz-menu__item--current:active) {color: rgba(255,255,255,1);}
	.brz .brz-css-1kwae1d .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active):hover > .brz-mm-listitem__text {color: rgba(255,255,255,1);}
	.brz .brz-css-1kwae1d  .brz-menu__item.brz-menu__item--current:not(brz-menu__item.brz-menu__item--current:active) > .brz-mm-listitem__text > .brz-icon-svg.brz-icon-svg-custom {background-color: rgba(255,255,255,1);}}
@media (max-width:767px) {.brz .brz-css-1kwae1d .brz-menu__item {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}
	.brz .brz-css-1kwae1d .brz-menu__item .brz-a {justify-content: center;text-align: center;}
	.brz .brz-css-1kwae1d .brz-mm-menu__item__icon {margin: 0 4px 0 0;font-size: 20px;}
	.brz .brz-css-1kwae1d .brz-mm-navbar {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1kwae1d.brz-mm-menu .brz-mm-listitem_vertical .brz-mm-btn_next {height: calc(var(--brz-ugudlcdcxlbqmobilelineheight,initial) * var(--brz-ugudlcdcxlbqmobilefontsize,initial) + 15px + 15px);padding-right: 20px;}}
.brz .brz-css-jow5dd {flex: 1 1 6.7%;max-width: 6.7%;justify-content: center;}
.brz .brz-css-jow5dd .brz-columns__scroll-effect {justify-content: center;}
@media (min-width:991px) {.brz .brz-css-jow5dd {z-index: auto;flex: 1 1 6.7%;max-width: 6.7%;justify-content: center;}
	.brz .brz-css-jow5dd .brz-columns__scroll-effect {justify-content: center;}
	.brz .brz-css-jow5dd > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-jow5dd:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-jow5dd > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-jow5dd > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-jow5dd > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-jow5dd > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-jow5dd:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-jow5dd:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-jow5dd > * {display: none;}
	.brz .brz-css-jow5dd > .brz-column__items {display: none;}}
@media (max-width:767px) {.brz .brz-css-jow5dd {flex: 1 1 100%;max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-jow5dd > * {display: none;}
	.brz .brz-css-jow5dd > .brz-column__items {display: none;}}
.brz .brz-css-190lc1z {padding: 0;}
@media (min-width:991px) {.brz .brz-css-190lc1z {z-index: auto;margin: 0;border: 0px solid transparent;padding: 0;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-190lc1z:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-190lc1z {padding: 5px 15px 5px 15px;}}
.brz .brz-css-xmdiqp {margin: 0;}
.brz .brz-css-xmdiqp .brz-wrapper-transform {--transform-flipHorizontal: 1;--transform-flipVertical: 1;--transform-anchor-pointX: center;--transform-anchor-pointY: center;transform: scaleX(calc(var(--transform-flipHorizontal) * 1)) scaleY(calc(var(--transform-flipVertical) * 1));transform-origin: var(--transform-anchor-pointY) var(--transform-anchor-pointX);}
@media (min-width:991px) {.brz .brz-css-xmdiqp {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-xmdiqp .brz-wrapper-transform {--transform-flipHorizontal: 1;--transform-flipVertical: 1;--transform-anchor-pointX: center;--transform-anchor-pointY: center;transform: scaleX(calc(var(--transform-flipHorizontal) * 1)) scaleY(calc(var(--transform-flipVertical) * 1));transform-origin: var(--transform-anchor-pointY) var(--transform-anchor-pointX);}}
@media (min-width:991px) {.brz .brz-css-xmdiqp {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-xmdiqp {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-xmdiqp {margin: 10px 0px 10px 0px;}}
.brz .brz-css-nntapz {flex-direction: row;}
.brz .brz-css-nntapz .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}
.brz .brz-css-1umndxb .brz-icon__container {margin-left: auto;margin-right: 2px;}
@media (min-width:991px) {.brz .brz-css-1umndxb {flex-direction: row;}
	.brz .brz-css-1umndxb .brz-icon__container {margin-left: auto;margin-right: 2px;align-items: flex-start;}}
.brz .brz-css-e9bk1k {font-size: 48px;padding: 0px;border-radius: 0;stroke-width: 1;}
.brz .brz-css-e9bk1k {color: rgba(var(--brz-global-color3),1);border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}
.brz .brz-css-e9bk1k .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-e9bk1k {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-e9bk1k .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-e9bk1k:hover {color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-e9bk1k:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-e9bk1k {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-e9bk1k .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:767px) {.brz .brz-css-e9bk1k {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-e9bk1k .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-h3q0h5 {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-h3q0h5 {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-h3q0h5:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-h3q0h5:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-h3q0h5:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-10fnxcx {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-1n6htfm {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-of8RQ {margin-top: 0px !important;margin-bottom: 0px !important;text-align: justify !important;font-family: "Comfortaa",display !important;font-size: 16px;line-height: 1.7;font-weight: 400;letter-spacing: -.2px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;}
@media (min-width:991px) {.brz .brz-css-of8RQ {margin-top: 0px !important;margin-bottom: 0px !important;text-align: justify !important;font-family: "Comfortaa",display !important;font-size: 16px;line-height: 1.7;font-weight: 400;letter-spacing: -.2px;font-variation-settings: "wght" 400,"wdth" 100,"SOFT" 0;text-transform: inherit !important;}}
.brz .brz-css-r8i1ey {margin: 0;}
@media (min-width:991px) {.brz .brz-css-r8i1ey {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-r8i1ey .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-r8i1ey {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-r8i1ey {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-r8i1ey {margin: 10px 0px 10px 0px;}}
.brz .brz-css-zwba1n {width: 100%;min-height: 100%;}
.brz .brz-css-zwba1n:before {border-radius: 0px;}
.brz .brz-css-zwba1n:before {box-shadow: none;border: 0px solid rgba(220,222,225,1);}
.brz .brz-css-zwba1n .brz-embed-content {padding: 0;border-radius: 0px;overflow: hidden;}
.brz .brz-css-zwba1n .brz-embed-content {background-color: rgba(var(--brz-global-color2),0);background-image: none;}
@media (min-width:991px) {.brz .brz-css-zwba1n:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-zwba1n:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-zwba1n:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (min-width:991px) {.brz .brz-css-3bcg12 {width: 100%;min-height: 100%;}
	.brz .brz-css-3bcg12:before {border-radius: 0px;}
	.brz .brz-css-3bcg12:hover:before {box-shadow: none;border: 0px solid rgba(220,222,225,1);}
	.brz .brz-css-3bcg12 .brz-embed-content {padding: 0;border-radius: 0px;overflow: hidden;}
	.brz .brz-css-3bcg12:hover .brz-embed-content {background-color: rgba(var(--brz-global-color2),0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-3bcg12:hover:before {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-2sdt19 {z-index: auto;margin: 0;}
.brz .brz-css-2sdt19.brz-section .brz-section__content {min-height: auto;display: flex;}
.brz .brz-css-2sdt19 .brz-container {justify-content: center;}
.brz .brz-css-2sdt19 > .slick-slider > .brz-slick-slider__dots {color: rgba(0,0,0,1);}
.brz .brz-css-2sdt19 > .slick-slider > .brz-slick-slider__arrow {color: rgba(0,0,0,.7);}
@media (min-width:991px) {.brz .brz-css-2sdt19 {display: block;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-2sdt19 {display: block;}}
@media (max-width:767px) {.brz .brz-css-2sdt19 {display: block;}}
@media (min-width:991px) {.brz .brz-css-1ij56rs {z-index: auto;margin: 0;}
	.brz .brz-css-1ij56rs.brz-section .brz-section__content {min-height: auto;display: flex;}
	.brz .brz-css-1ij56rs .brz-container {justify-content: center;}
	.brz .brz-css-1ij56rs > .slick-slider > .brz-slick-slider__dots:hover {color: rgba(0,0,0,1);}
	.brz .brz-css-1ij56rs > .slick-slider > .brz-slick-slider__arrow:hover {color: rgba(0,0,0,.7);}}
@media (min-width:991px) {.brz .brz-css-1ij56rs:hover {display: block;}}
.brz .brz-css-guytnn {padding: 75px 0px 75px 0px;}
.brz .brz-css-guytnn > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
.brz .brz-css-guytnn > .brz-bg {border: 0px solid rgba(102,115,141,0);}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
.brz .brz-css-guytnn > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}
@media (min-width:991px) {.brz .brz-css-guytnn > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-guytnn {padding: 50px 15px 50px 15px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-guytnn > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-guytnn {padding: 25px 15px 25px 15px;}}
@media (max-width:767px) {.brz .brz-css-guytnn > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-guytnn > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1xl9s1b {padding: 116px 0px 128px 0px;}
.brz .brz-css-1xl9s1b > .brz-bg {border-width: 1px 0px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color6),1);}
.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),.1);}
@media (min-width:991px) {.brz .brz-css-1xl9s1b {padding: 116px 0px 128px 0px;}
	.brz .brz-css-1xl9s1b > .brz-bg {border-radius: 0px;mix-blend-mode: normal;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg {border-width: 1px 0px 0px 0px;border-style: solid;border-color: rgba(var(--brz-global-color6),1);}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-color {background-color: rgba(var(--brz-global-color8),.1);background-image: none;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-shape__top {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(0deg) rotateY(0deg);z-index: auto;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-shape__top::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-shape__bottom {background-size: 100% 100px;height: 100px;transform: scale(1.02) rotateX(-180deg) rotateY(-180deg);z-index: auto;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-shape__bottom::after {background-image: none;-webkit-mask-image: none;background-size: 100% 100px;height: 100px;}
	.brz .brz-css-1xl9s1b > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {display: none;background-position: 50% 50%;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-slideshow  .brz-bg-slideshow-item {filter: none;}}
@media (min-width:991px) {.brz .brz-css-1xl9s1b:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-image {background-attachment: scroll;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-map {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1xl9s1b:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1xl9s1b {padding: 75px 40px 35px 40px;}}
@media (max-width:767px) {.brz .brz-css-1xl9s1b {padding: 24px 24px 72px 24px;}}
.brz .brz-css-1luw0hj {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-1luw0hj {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1luw0hj {max-width: 100%;}}
@media (max-width:767px) {.brz .brz-css-1luw0hj {max-width: 100%;}}
.brz .brz-css-8dnozk {border-width: 1px 0px 0px 0px;border-style: solid;border-color: transparent;}
@media (min-width:991px) {.brz .brz-css-8dnozk:hover {border-width: 1px 0px 0px 0px;border-style: solid;border-color: transparent;}}
@media (min-width:991px) {.brz .brz-css-8dnozk {max-width: calc(1 * var(--brz-section-container-max-width,1170px));}}
.brz .brz-css-10e8jpe {margin: 0;z-index: auto;align-items: flex-start;}
.brz .brz-css-10e8jpe > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
.brz .brz-css-10e8jpe > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {filter: none;}
.brz .brz-css-10e8jpe > .brz-row {border: 0px solid transparent;}
@media (min-width:991px) {.brz .brz-css-10e8jpe {min-height: auto;display: flex;}
	.brz .brz-css-10e8jpe > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-10e8jpe {min-height: auto;display: flex;}
	.brz .brz-css-10e8jpe > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (max-width:767px) {.brz .brz-css-10e8jpe {min-height: auto;display: flex;}
	.brz .brz-css-10e8jpe > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-10e8jpe > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;flex-direction: row;flex-wrap: wrap;justify-content: flex-start;}}
@media (min-width:991px) {.brz .brz-css-1an4vvi {margin: 0;z-index: auto;align-items: flex-start;}
	.brz .brz-css-1an4vvi > .brz-bg {border-radius: 0px;max-width: 100%;mix-blend-mode: normal;}
	.brz .brz-css-1an4vvi:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-1an4vvi > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-1an4vvi > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-1an4vvi > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-1an4vvi > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-video {filter: none;}
	.brz .brz-css-1an4vvi:hover > .brz-row {border: 0px solid transparent;}}
@media (min-width:991px) {.brz .brz-css-1an4vvi {min-height: auto;display: flex;}
	.brz .brz-css-1an4vvi:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1an4vvi:hover > .brz-bg > .brz-bg-video {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1an4vvi:hover > .brz-row {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1mgbab6 {padding: 10px;max-width: 100%;}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1mgbab6 {padding: 0;}}
@media (max-width:767px) {.brz .brz-css-1mgbab6 {padding: 0;}}
.brz .brz-css-187yhb2 {padding: 0;}
@media (min-width:991px) {.brz .brz-css-1gohfum {padding: 0;max-width: 100%;}}
.brz .brz-css-1vf6zvc {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
.brz .brz-css-1vf6zvc .brz-columns__scroll-effect {justify-content: flex-start;}
.brz .brz-css-1vf6zvc > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
.brz .brz-css-1vf6zvc > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-map {display: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-map {filter: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-video {display: none;}
.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-video {filter: none;}
@media (min-width:991px) {.brz .brz-css-1vf6zvc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1vf6zvc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-1vf6zvc {flex: 1 1 100%;max-width: 100%;}
	.brz .brz-css-1vf6zvc > .brz-bg {margin: 10px 0px 10px 0px;}}
@media (max-width:767px) {.brz .brz-css-1vf6zvc > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-1vf6zvc > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-llznuj > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-llznuj {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
	.brz .brz-css-llznuj .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-llznuj > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-llznuj:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-llznuj > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-llznuj > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-llznuj > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-llznuj > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-llznuj:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-llznuj:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-m57ec7 {z-index: auto;margin: 0;border: 0px solid transparent;padding: 5px 15px 5px 15px;min-height: 100%;}
@media (min-width:991px) {.brz .brz-css-m57ec7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-m57ec7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:767px) {.brz .brz-css-m57ec7 {margin: 10px 0px 10px 0px;padding: 0;}}
@media (max-width:767px) {.brz .brz-css-m57ec7 {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-1z06xgw {margin: 0;padding: 16px;}
@media (min-width:991px) {.brz .brz-css-1z06xgw {z-index: auto;margin: 0;border: 0px solid transparent;padding: 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-1z06xgw:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1z06xgw {padding: 8px;}}
@media (max-width:767px) {.brz .brz-css-1z06xgw {padding: 25px 0px 0px 0px;}}
.brz .brz-css-1u7d53e {padding: 0;margin: 10px 0px 10px 0px;justify-content: center;position: relative;}
.brz .brz-css-1u7d53e .brz-wrapper-transform {transform: none;}
@media (min-width:991px) {.brz .brz-css-1u7d53e {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1u7d53e {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1u7d53e {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-jvzud0 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-jvzud0 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-jvzud0 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-jvzud0 {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-jvzud0 {margin: 0px 0px 8px 0px;}}
.brz .brz-css-1pjdngh {width: 100%;mix-blend-mode: normal;}
@media (min-width:991px) {.brz .brz-css-vuz851 {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-nwoKe {margin-top: 0px !important;margin-bottom: 0px !important;text-align: right !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-nwoKe {margin-top: 0px !important;margin-bottom: 0px !important;text-align: right !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-nwoKe {font-size: var(--brz-subtitletabletfontsize,initial);line-height: var(--brz-subtitletabletlineheight,initial);font-weight: var(--brz-subtitletabletfontweight,initial);font-weight: var(--brz-subtitletabletbold,initial);letter-spacing: var(--brz-subtitletabletletterspacing,initial);font-variation-settings: var(--brz-subtitletabletfontvariation,initial);font-style: var(--brz-subtitletabletitalic,initial);text-decoration: var(--brz-subtitletablettextdecoration,initial) !important;text-transform: var(--brz-subtitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-nwoKe {text-align: justify !important;font-size: var(--brz-subtitlemobilefontsize,initial);line-height: var(--brz-subtitlemobilelineheight,initial);font-weight: var(--brz-subtitlemobilefontweight,initial);font-weight: var(--brz-subtitlemobilebold,initial);letter-spacing: var(--brz-subtitlemobileletterspacing,initial);font-variation-settings: var(--brz-subtitlemobilefontvariation,initial);font-style: var(--brz-subtitlemobileitalic,initial);text-decoration: var(--brz-subtitlemobiletextdecoration,initial) !important;text-transform: var(--brz-subtitlemobiletexttransform,initial) !important;}}
.brz .brz-css-slzu2l {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1b6471k {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1b6471k .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1b6471k {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1b6471k {display: none;}}
.brz .brz-css-z59yr {height: 50px;}
.brz .brz-css-h5jkn6 {height: 20px;}
@media (min-width:991px) {.brz .brz-css-h5jkn6 {height: 20px;}}
@media (max-width:767px) {.brz .brz-css-h5jkn6 {height: 15px;}}
.brz .brz-css-facpgg {z-index: auto;position: relative;margin: 10px 0px 10px 0px;justify-content: center;padding: 0;gap: 20px 10px;}
@media (min-width:991px) {.brz .brz-css-facpgg {position: relative;}
	.brz .brz-css-facpgg {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-facpgg {position: relative;}
	.brz .brz-css-facpgg {display: flex;}}
@media (max-width:767px) {.brz .brz-css-facpgg {position: relative;}
	.brz .brz-css-facpgg {display: flex;}}
.brz .brz-css-18y62i7 {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-18y62i7 {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-18y62i7 {position: relative;}
	.brz .brz-css-18y62i7:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-18y62i7 {justify-content: flex-start;}}
.brz .brz-css-1v6popm {justify-content: center;padding: 0;gap: 20px 10px;}
.brz .brz-css-4i66os {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-4i66os {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-4i66os {justify-content: flex-start;}}
.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1n97tm4.brz-btn--hover-in:before {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1n97tm4.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
@media (min-width:991px) {.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1n97tm4.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1n97tm4.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1n97tm4.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1n97tm4.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-back-pulse:before {animation-duration: .6s;}}
@media (max-width:767px) {.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1n97tm4.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1n97tm4.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1n97tm4.brz-back-pulse:before {animation-duration: .6s;}}
.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1yvwm5t.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1yvwm5t.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1yvwm5t.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1yvwm5t.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1yvwm5t.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1yvwm5t.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1yvwm5t.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1yvwm5t.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-pa6524.brz-btn {font-family: var(--brz-buttonfontfamily,initial);font-weight: var(--brz-buttonfontweight,initial);font-weight: var(--brz-buttonbold,initial);font-size: var(--brz-buttonfontsize,initial);line-height: var(--brz-buttonlineheight,initial);letter-spacing: var(--brz-buttonletterspacing,initial);font-variation-settings: var(--brz-buttonfontvariation,initial);font-style: var(--brz-buttonitalic,initial);text-decoration: var(--brz-buttontextdecoration,initial) !important;text-transform: var(--brz-buttontexttransform,initial) !important;border-radius: 0;padding: 14px 42px 14px 42px;padding: 14px 42px;flex-flow: row-reverse nowrap;}
.brz .brz-css-pa6524.brz-btn {display: flex;color: rgba(var(--brz-global-color8),1);border: 2px solid rgba(var(--brz-global-color3),1);box-shadow: none;}
.brz .brz-css-pa6524.brz-btn:not(.brz-btn--hover) {background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-pa6524.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}
.brz .brz-css-pa6524.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color8),1);background-color: rgba(var(--brz-global-color3),1);background-image: none;}
.brz .brz-css-pa6524:after {height: unset;}
.brz .brz-css-pa6524 .brz-btn--story-container {border: 2px solid rgba(var(--brz-global-color3),1);flex-flow: row-reverse nowrap;border-radius: 0;}
.brz .brz-css-pa6524 .brz-btn--story-container:after {height: unset;}
@media (min-width:991px) {.brz .brz-css-pa6524.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (min-width:991px) {.brz .brz-css-pa6524.brz-btn:not(.brz-btn--hover):hover {background-color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-pa6524.brz-btn.brz-btn-submit:hover {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-pa6524.brz-btn {font-weight: var(--brz-buttontabletfontweight,initial);font-weight: var(--brz-buttontabletbold,initial);font-size: var(--brz-buttontabletfontsize,initial);line-height: var(--brz-buttontabletlineheight,initial);letter-spacing: var(--brz-buttontabletletterspacing,initial);font-variation-settings: var(--brz-buttontabletfontvariation,initial);font-style: var(--brz-buttontabletitalic,initial);text-decoration: var(--brz-buttontablettextdecoration,initial) !important;text-transform: var(--brz-buttontablettexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-pa6524.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:767px) {.brz .brz-css-pa6524.brz-btn {font-weight: var(--brz-buttonmobilefontweight,initial);font-weight: var(--brz-buttonmobilebold,initial);font-size: var(--brz-buttonmobilefontsize,initial);line-height: var(--brz-buttonmobilelineheight,initial);letter-spacing: var(--brz-buttonmobileletterspacing,initial);font-variation-settings: var(--brz-buttonmobilefontvariation,initial);font-style: var(--brz-buttonmobileitalic,initial);text-decoration: var(--brz-buttonmobiletextdecoration,initial) !important;text-transform: var(--brz-buttonmobiletexttransform,initial) !important;padding: 11px 26px 11px 26px;padding: 11px 26px;}}
@media (max-width:767px) {.brz .brz-css-pa6524.brz-btn {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524.brz-btn.brz-btn-submit {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-pa6524 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
.brz .brz-css-miyt54.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-miyt54.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-miyt54.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-miyt54.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-miyt54.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-miyt54 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-miyt54.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-miyt54.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-miyt54.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-miyt54.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-miyt54.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-miyt54:after {height: unset;}
	.brz .brz-css-miyt54 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-miyt54 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-miyt54.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-miyt54.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-miyt54.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-miyt54 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-miyt54.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-miyt54.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-127ypgu {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-127ypgu {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-127ypgu {position: relative;}
	.brz .brz-css-127ypgu:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-127ypgu {justify-content: flex-start;}}
.brz .brz-css-1588yx1 {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1588yx1 {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-1588yx1 {justify-content: flex-start;}}
.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-pj9c4m.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-pj9c4m.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-pj9c4m.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-pj9c4m.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-pj9c4m.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-pj9c4m.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-pj9c4m.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-pj9c4m.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1ekb251.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-1ekb251.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1ekb251.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1ekb251.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1ekb251.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1ekb251 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-1ekb251.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1ekb251.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-1ekb251.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1ekb251.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1ekb251.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1ekb251:after {height: unset;}
	.brz .brz-css-1ekb251 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1ekb251 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1ekb251.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1ekb251.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1ekb251.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1ekb251 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1ekb251.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1ekb251.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-y8fpl2 {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-y8fpl2 {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-y8fpl2 {position: relative;}
	.brz .brz-css-y8fpl2:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-y8fpl2 {justify-content: flex-start;}}
.brz .brz-css-1ymmhet {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1ymmhet {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-1ymmhet {justify-content: flex-start;}}
.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1nox22q.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1nox22q.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1nox22q.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1nox22q.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-1nox22q.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-1nox22q.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-1nox22q.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-1nox22q.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1lfjiu8.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-1lfjiu8.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1lfjiu8.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1lfjiu8.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1lfjiu8.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1lfjiu8 .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-1lfjiu8.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1lfjiu8.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-1lfjiu8.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1lfjiu8.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1lfjiu8.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1lfjiu8:after {height: unset;}
	.brz .brz-css-1lfjiu8 .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1lfjiu8 .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1lfjiu8.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1lfjiu8.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1lfjiu8.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1lfjiu8 .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1lfjiu8.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1lfjiu8.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1uq4lhf {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1uq4lhf {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1uq4lhf {position: relative;}
	.brz .brz-css-1uq4lhf:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1uq4lhf {justify-content: flex-start;}}
.brz .brz-css-10cgi2q {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-10cgi2q {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-10cgi2q {justify-content: flex-start;}}
.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-7pqe0i.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-7pqe0i.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-7pqe0i.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-7pqe0i.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-7pqe0i.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-7pqe0i.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-7pqe0i.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-7pqe0i.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-5v297a.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-5v297a.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-5v297a.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-5v297a.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-5v297a.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-5v297a .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-5v297a.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-5v297a.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-5v297a.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-5v297a.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-5v297a.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-5v297a:after {height: unset;}
	.brz .brz-css-5v297a .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-5v297a .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-5v297a.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-5v297a.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-5v297a.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-5v297a .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-5v297a.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-5v297a.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-1wim2wv {margin: 0;justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-1wim2wv {z-index: auto;position: relative;margin: 0;justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-1wim2wv {position: relative;}
	.brz .brz-css-1wim2wv:hover {display: flex;}}
@media (max-width:767px) {.brz .brz-css-1wim2wv {justify-content: flex-start;}}
.brz .brz-css-177x44y {justify-content: flex-end;}
@media (min-width:991px) {.brz .brz-css-177x44y {justify-content: flex-end;padding: 0;gap: 20px 10px;}}
@media (max-width:767px) {.brz .brz-css-177x44y {justify-content: flex-start;}}
.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-l98slo.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;}
.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-l98slo.brz-btn--hover-in {background-color: rgba(var(--brz-global-color7),1);background: transparent;}
@media (min-width:991px) {.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-l98slo.brz-btn--hover-in:before {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background-image: none;}
	.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-l98slo.brz-btn--hover-in {background-color: rgba(var(--brz-global-color3),1);background: transparent;}}
@media (min-width:991px) {.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in), .brz .brz-css-l98slo.brz-btn--hover-in:before {transition-duration: .6s;}
	.brz .brz-css-l98slo.brz-btn--hover:not(.brz-btn--hover-in):before, .brz .brz-css-l98slo.brz-btn--hover-in {transition-duration: .6s;}
	.brz .brz-css-l98slo.brz-back-pulse:before:hover {animation-duration: .6s;}}
.brz .brz-css-1aatpbp.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;}
.brz .brz-css-1aatpbp.brz-btn {color: rgba(var(--brz-global-color7),1);border: 0px solid rgba(35,157,219,0);}
.brz .brz-css-1aatpbp.brz-btn:not(.brz-btn--hover) {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1aatpbp.brz-btn .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color7),1);}
.brz .brz-css-1aatpbp.brz-btn.brz-btn-submit {color: rgba(var(--brz-global-color7),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
.brz .brz-css-1aatpbp .brz-btn--story-container {border: 0;border-radius: 0 !important;}
@media (min-width:991px) {.brz .brz-css-1aatpbp.brz-btn {font-family: var(--brz-ugudlcdcxlbqfontfamily,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;padding: 5px 0px 5px 0px;padding: 5px 0px;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1aatpbp.brz-btn:hover {display: flex;color: rgba(var(--brz-global-color2),1);border: 0px solid rgba(var(--brz-global-color3),0);box-shadow: none;}
	.brz .brz-css-1aatpbp.brz-btn:not(.brz-btn--hover):hover {border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1aatpbp.brz-btn:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
	.brz .brz-css-1aatpbp.brz-btn.brz-btn-submit:hover {color: rgba(var(--brz-global-color2),1);border: 0 !important;background-color: transparent !important;box-shadow: none !important;background: transparent;}
	.brz .brz-css-1aatpbp:after {height: unset;}
	.brz .brz-css-1aatpbp .brz-btn--story-container {border: 0;border-radius: 0 !important;flex-flow: row-reverse nowrap;}
	.brz .brz-css-1aatpbp .brz-btn--story-container:after {height: unset;}}
@media (min-width:991px) {.brz .brz-css-1aatpbp.brz-btn:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1aatpbp.brz-btn:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1aatpbp.brz-btn.brz-btn-submit:hover {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}
	.brz .brz-css-1aatpbp .brz-btn--story-container {transition-duration: .5s;transition-property: filter,color,background,border-color,box-shadow;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1aatpbp.brz-btn {font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;padding: 0;padding: 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-1aatpbp.brz-btn {font-family: var(--brz-u9xihr8qxhssfontfamily,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;padding: 3px 0px 3px 0px;padding: 3px 0px;}}
.brz .brz-css-148dahv > .brz-bg {margin: 0;}
@media (min-width:991px) {.brz .brz-css-148dahv {z-index: auto;flex: 1 1 50%;max-width: 50%;justify-content: flex-start;}
	.brz .brz-css-148dahv .brz-columns__scroll-effect {justify-content: flex-start;}
	.brz .brz-css-148dahv > .brz-bg {margin: 0;mix-blend-mode: normal;border-radius: 0px;}
	.brz .brz-css-148dahv:hover > .brz-bg {border: 0px solid rgba(102,115,141,0);box-shadow: none;}
	.brz .brz-css-148dahv > .brz-bg > .brz-bg-image {background-size: cover;background-repeat: no-repeat;-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-image {background-image: none;filter: none;display: block;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-image:after {content: "";background-image: none;}
	.brz .brz-css-148dahv > .brz-bg > .brz-bg-color {-webkit-mask-image: none;mask-image: none;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-color {background-color: rgba(0,0,0,0);background-image: none;}
	.brz .brz-css-148dahv > .brz-bg > .brz-bg-map {display: none;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-map {filter: none;}
	.brz .brz-css-148dahv > .brz-bg > .brz-bg-video {display: none;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-video {filter: none;}}
@media (min-width:991px) {.brz .brz-css-148dahv:hover > .brz-bg {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-image {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}
	.brz .brz-css-148dahv:hover > .brz-bg > .brz-bg-color {transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
.brz .brz-css-qweajh {margin: 0;padding: 16px;}
@media (min-width:991px) {.brz .brz-css-qweajh {z-index: auto;margin: 0;border: 0px solid transparent;padding: 16px;min-height: 100%;}}
@media (min-width:991px) {.brz .brz-css-qweajh:hover {display: flex;transition-duration: .5s;transition-property: filter,box-shadow,background,border-radius,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-qweajh {padding: 8px;}}
@media (max-width:767px) {.brz .brz-css-qweajh {padding: 25px 0px 0px 0px;}}
.brz .brz-css-1y48u96 {margin: 0;}
@media (min-width:991px) {.brz .brz-css-1y48u96 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1y48u96 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1y48u96 {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1y48u96 {margin: 0px 0px 8px 0px;}}
@media (min-width:991px) {.brz .brz-css-tvsjwg {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-bnGyO {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-bnGyO {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-subtitlefontfamily,initial) !important;font-size: var(--brz-subtitlefontsize,initial);line-height: var(--brz-subtitlelineheight,initial);font-weight: var(--brz-subtitlefontweight,initial);font-weight: var(--brz-subtitlebold,initial);letter-spacing: var(--brz-subtitleletterspacing,initial);font-variation-settings: var(--brz-subtitlefontvariation,initial);font-style: var(--brz-subtitleitalic,initial);text-decoration: var(--brz-subtitletextdecoration,initial) !important;text-transform: var(--brz-subtitletexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-bnGyO {font-size: var(--brz-subtitletabletfontsize,initial);line-height: var(--brz-subtitletabletlineheight,initial);font-weight: var(--brz-subtitletabletfontweight,initial);font-weight: var(--brz-subtitletabletbold,initial);letter-spacing: var(--brz-subtitletabletletterspacing,initial);font-variation-settings: var(--brz-subtitletabletfontvariation,initial);font-style: var(--brz-subtitletabletitalic,initial);text-decoration: var(--brz-subtitletablettextdecoration,initial) !important;text-transform: var(--brz-subtitletablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-bnGyO {text-align: justify !important;font-size: var(--brz-subtitlemobilefontsize,initial);line-height: var(--brz-subtitlemobilelineheight,initial);font-weight: var(--brz-subtitlemobilefontweight,initial);font-weight: var(--brz-subtitlemobilebold,initial);letter-spacing: var(--brz-subtitlemobileletterspacing,initial);font-variation-settings: var(--brz-subtitlemobilefontvariation,initial);font-style: var(--brz-subtitlemobileitalic,initial);text-decoration: var(--brz-subtitlemobiletextdecoration,initial) !important;text-transform: var(--brz-subtitlemobiletexttransform,initial) !important;}}
.brz .brz-css-19rtcdj {margin: 20px 0px 8px -2px;justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-19rtcdj {z-index: auto;position: relative;margin: 20px 0px 8px -2px;justify-content: flex-start;padding: 0;gap: 20px 10px;}}
@media (min-width:991px) {.brz .brz-css-19rtcdj {position: relative;}
	.brz .brz-css-19rtcdj:hover {display: flex;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-19rtcdj {margin: 8px 0px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-19rtcdj {margin: 0;}}
.brz .brz-css-mw4u11 {justify-content: flex-start;}
@media (min-width:991px) {.brz .brz-css-mw4u11 {justify-content: flex-start;padding: 0;gap: 20px 10px;}}
.brz .brz-css-sqbbv8 {font-size: 48px;padding: 0px;border-radius: 0;stroke-width: 1;}
.brz .brz-css-sqbbv8 {color: rgba(var(--brz-global-color3),1);border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}
.brz .brz-css-sqbbv8 .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),1);}
@media (min-width:991px) {.brz .brz-css-sqbbv8 {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-sqbbv8 .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-sqbbv8:hover {color: rgba(var(--brz-global-color3),.8);}
	.brz .brz-css-sqbbv8:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color3),.8);}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-sqbbv8 {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-sqbbv8 .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:767px) {.brz .brz-css-sqbbv8 {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-sqbbv8 .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
.brz .brz-css-j93mke {font-size: 32px;padding: 14px;border-radius: 100px;}
.brz .brz-css-j93mke {color: rgba(var(--brz-global-color2),1);background-color: rgba(var(--brz-global-color7),.12);}
.brz .brz-css-j93mke .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-j93mke {font-size: 32px;padding: 14px;border-radius: 100px;stroke-width: 1;}
	.brz .brz-css-j93mke:hover {color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(var(--brz-global-color3),1);box-shadow: none;background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-j93mke:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (min-width:991px) {.brz .brz-css-j93mke:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-j93mke:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-j93mke {font-size: 14px;}}
@media (max-width:767px) {.brz .brz-css-j93mke {font-size: 27px;}}
.brz .brz-css-gjsln7 {font-size: 32px;padding: 14px;border-radius: 100px;}
.brz .brz-css-gjsln7 {color: rgba(var(--brz-global-color2),1);background-color: rgba(var(--brz-global-color7),.13);}
.brz .brz-css-gjsln7 .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color2),1);}
@media (min-width:991px) {.brz .brz-css-gjsln7 {font-size: 32px;padding: 14px;border-radius: 100px;stroke-width: 1;}
	.brz .brz-css-gjsln7:hover {color: rgba(var(--brz-global-color8),1);border: 0px solid rgba(var(--brz-global-color3),1);box-shadow: none;background-color: rgba(var(--brz-global-color2),1);background-image: none;}
	.brz .brz-css-gjsln7:hover .brz-icon-svg-custom {background-color: rgba(var(--brz-global-color8),1);}}
@media (min-width:991px) {.brz .brz-css-gjsln7:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-gjsln7:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-gjsln7 {font-size: 14px;}}
@media (max-width:767px) {.brz .brz-css-gjsln7 {font-size: 26px;}}
@media (min-width:991px) {.brz .brz-css-1m3u1b6 {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-1m3u1b6 .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1m3u1b6 {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-zwnm8i {height: 10px;}
@media (min-width:991px) {.brz .brz-css-zwnm8i {height: 10px;}}
@media (max-width:767px) {.brz .brz-css-zwnm8i {height: 15px;}}
.brz .brz-css-qxobrp {margin: 0;}
@media (min-width:991px) {.brz .brz-css-qxobrp {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-qxobrp .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-qxobrp {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-lc1q61 {flex-direction: row;}
.brz .brz-css-lc1q61 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}
.brz .brz-css-p80si6 .brz-icon__container {margin-left: auto;margin-right: 20px;}
@media (min-width:991px) {.brz .brz-css-p80si6 {flex-direction: row;}
	.brz .brz-css-p80si6 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}}
@media (max-width:767px) {.brz .brz-css-p80si6 .brz-icon__container {margin-left: auto;margin-right: 12px;}}
.brz .brz-css-1w201we {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-1w201we {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-1w201we:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1w201we:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1w201we:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-10df8lu {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-qLxIb {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-qLxIb {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-qLxIb {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-qLxIb {text-align: justify !important;font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
.brz .brz-css-7xvgik {margin: 2px 0px 8px 0px;}
@media (min-width:991px) {.brz .brz-css-7xvgik {padding: 0;margin: 2px 0px 8px 0px;justify-content: center;position: relative;}
	.brz .brz-css-7xvgik .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-7xvgik {display: flex;z-index: auto;position: relative;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-7xvgik {margin: 2px 0px 0px 0px;}}
@media (max-width:767px) {.brz .brz-css-7xvgik {margin: 2px 0px 0px 0px;}}
.brz .brz-css-33vbr4 .brz-icon__container {margin-left: auto;margin-right: 20px;}
@media (min-width:991px) {.brz .brz-css-33vbr4 {flex-direction: row;}
	.brz .brz-css-33vbr4 .brz-icon__container {margin-left: auto;margin-right: 20px;align-items: flex-start;}}
@media (max-width:767px) {.brz .brz-css-33vbr4 .brz-icon__container {margin-left: auto;margin-right: 12px;}}
.brz .brz-css-1hdkxre {font-size: 20px;}
@media (min-width:991px) {.brz .brz-css-1hdkxre {font-size: 20px;padding: 0px;border-radius: 0;stroke-width: 1;}
	.brz .brz-css-1hdkxre:hover {border: 0px solid rgba(35,157,219,0);box-shadow: none;background-color: rgba(189,225,244,0);background-image: none;}}
@media (min-width:991px) {.brz .brz-css-1hdkxre:hover {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}
	.brz .brz-css-1hdkxre:hover .brz-icon-svg-custom {transition-duration: .5s;transition-property: color,box-shadow,background,border,border-color;}}
@media (min-width:991px) {.brz .brz-css-mqvrob {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-fMQkB {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-fMQkB {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-fMQkB {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-fMQkB {text-align: justify !important;font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
.brz .brz-css-1yid6jy {margin: 2px 0px 0px 0px;}
@media (min-width:991px) {.brz .brz-css-1yid6jy {padding: 0;margin: 2px 0px 0px 0px;justify-content: center;position: relative;}
	.brz .brz-css-1yid6jy .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-1yid6jy {display: flex;z-index: auto;position: relative;}}
@media (max-width:767px) {.brz .brz-css-1yid6jy {margin: 8px 0px 0px 0px;}}
@media (min-width:991px) {.brz .brz-css-mpjmry {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-y6prp {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-y6prp {margin-top: 0px !important;margin-bottom: 0px !important;text-align: left !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-y6prp {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-y6prp {font-size: var(--brz-ugudlcdcxlbqmobilefontsize,initial);line-height: var(--brz-ugudlcdcxlbqmobilelineheight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilefontweight,initial);font-weight: var(--brz-ugudlcdcxlbqmobilebold,initial);letter-spacing: var(--brz-ugudlcdcxlbqmobileletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqmobilefontvariation,initial);font-style: var(--brz-ugudlcdcxlbqmobileitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqmobiletextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqmobiletexttransform,initial) !important;}}
@media (min-width:991px) {.brz .brz-css-mbir7l {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-mbir7l .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-mbir7l {display: flex;z-index: auto;position: relative;}}
.brz .brz-css-1l3j2t3 {height: 46px;}
@media (min-width:991px) {.brz .brz-css-1l3j2t3 {height: 46px;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-1l3j2t3 {height: 75px;}}
@media (max-width:767px) {.brz .brz-css-1l3j2t3 {height: 45px;}}
.brz .brz-css-106cr9e {margin: 0;}
@media (min-width:991px) {.brz .brz-css-106cr9e {padding: 0;margin: 0;justify-content: center;position: relative;}
	.brz .brz-css-106cr9e .brz-wrapper-transform {transform: none;}}
@media (min-width:991px) {.brz .brz-css-106cr9e {display: flex;z-index: auto;position: relative;}}
@media (min-width:991px) {.brz .brz-css-o4546m {width: 100%;mix-blend-mode: normal;}}
.brz .brz-css-gyvoe {margin-top: 0px !important;margin-bottom: 0px !important;text-align: center !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}
@media (min-width:991px) {.brz .brz-css-gyvoe {margin-top: 0px !important;margin-bottom: 0px !important;text-align: center !important;font-family: var(--brz-ugudlcdcxlbqfontfamily,initial) !important;font-size: var(--brz-ugudlcdcxlbqfontsize,initial);line-height: var(--brz-ugudlcdcxlbqlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtexttransform,initial) !important;}}
@media (max-width:991px) and (min-width:768px) {.brz .brz-css-gyvoe {font-size: var(--brz-ugudlcdcxlbqtabletfontsize,initial);line-height: var(--brz-ugudlcdcxlbqtabletlineheight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletfontweight,initial);font-weight: var(--brz-ugudlcdcxlbqtabletbold,initial);letter-spacing: var(--brz-ugudlcdcxlbqtabletletterspacing,initial);font-variation-settings: var(--brz-ugudlcdcxlbqtabletfontvariation,initial);font-style: var(--brz-ugudlcdcxlbqtabletitalic,initial);text-decoration: var(--brz-ugudlcdcxlbqtablettextdecoration,initial) !important;text-transform: var(--brz-ugudlcdcxlbqtablettexttransform,initial) !important;}}
@media (max-width:767px) {.brz .brz-css-gyvoe {font-family: var(--brz-u9xihr8qxhssfontfamily,initial) !important;font-size: var(--brz-u9xihr8qxhssmobilefontsize,initial);line-height: var(--brz-u9xihr8qxhssmobilelineheight,initial);font-weight: var(--brz-u9xihr8qxhssmobilefontweight,initial);font-weight: var(--brz-u9xihr8qxhssmobilebold,initial);letter-spacing: var(--brz-u9xihr8qxhssmobileletterspacing,initial);font-variation-settings: var(--brz-u9xihr8qxhssmobilefontvariation,initial);font-style: var(--brz-u9xihr8qxhssmobileitalic,initial);text-decoration: var(--brz-u9xihr8qxhssmobiletextdecoration,initial) !important;text-transform: var(--brz-u9xihr8qxhssmobiletexttransform,initial) !important;}}</style><style class="brz-style brz-project__style-palette">.brz .brz-cp-color1, .brz .brz-bcp-color1 {color: rgb(var(--brz-global-color1));}
.brz .brz-bgp-color1 {background-color: rgb(var(--brz-global-color1));}
.brz .brz-cp-color2, .brz .brz-bcp-color2 {color: rgb(var(--brz-global-color2));}
.brz .brz-bgp-color2 {background-color: rgb(var(--brz-global-color2));}
.brz .brz-cp-color3, .brz .brz-bcp-color3 {color: rgb(var(--brz-global-color3));}
.brz .brz-bgp-color3 {background-color: rgb(var(--brz-global-color3));}
.brz .brz-cp-color4, .brz .brz-bcp-color4 {color: rgb(var(--brz-global-color4));}
.brz .brz-bgp-color4 {background-color: rgb(var(--brz-global-color4));}
.brz .brz-cp-color5, .brz .brz-bcp-color5 {color: rgb(var(--brz-global-color5));}
.brz .brz-bgp-color5 {background-color: rgb(var(--brz-global-color5));}
.brz .brz-cp-color6, .brz .brz-bcp-color6 {color: rgb(var(--brz-global-color6));}
.brz .brz-bgp-color6 {background-color: rgb(var(--brz-global-color6));}
.brz .brz-cp-color7, .brz .brz-bcp-color7 {color: rgb(var(--brz-global-color7));}
.brz .brz-bgp-color7 {background-color: rgb(var(--brz-global-color7));}
.brz .brz-cp-color8, .brz .brz-bcp-color8 {color: rgb(var(--brz-global-color8));}
.brz .brz-bgp-color8 {background-color: rgb(var(--brz-global-color8));}
:root {--brz-global-color1: 114,174,172;--brz-global-color2: 50,158,155;--brz-global-color3: 114,174,172;--brz-global-color4: 184,230,225;--brz-global-color5: 84,84,84;--brz-global-color6: 238,243,240;--brz-global-color7: 103,115,108;--brz-global-color8: 255,255,255;}
:root {--brz-paragraphfontfamily: "Comfortaa",display;--brz-paragraphfontsize: 18px;--brz-paragraphfontsizesuffix: px;--brz-paragraphfontweight: 300;--brz-paragraphletterspacing: -.2px;--brz-paragraphlineheight: 1.4;--brz-paragraphfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphtabletfontsize: 17px;--brz-paragraphtabletfontweight: 100;--brz-paragraphtabletletterspacing: 0px;--brz-paragraphtabletlineheight: 1.4;--brz-paragraphtabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphmobilefontsize: 16px;--brz-paragraphmobilefontweight: 100;--brz-paragraphmobileletterspacing: -.2px;--brz-paragraphmobilelineheight: 1.4;--brz-paragraphmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-paragraphstoryfontsize: 4.14%;--brz-paragraphbold: 300;--brz-paragraphitalic: inherit;--brz-paragraphtextdecoration: inherit;--brz-paragraphtexttransform: inherit;--brz-paragraphtabletbold: 100;--brz-paragraphtabletitalic: inherit;--brz-paragraphtablettextdecoration: inherit;--brz-paragraphtablettexttransform: inherit;--brz-paragraphmobilebold: 100;--brz-paragraphmobileitalic: inherit;--brz-paragraphmobiletextdecoration: inherit;--brz-paragraphmobiletexttransform: inherit;--brz-subtitlefontfamily: "Comfortaa",display;--brz-subtitlefontsize: 24px;--brz-subtitlefontsizesuffix: px;--brz-subtitlefontweight: 400;--brz-subtitleletterspacing: -.2px;--brz-subtitlelineheight: 1.4;--brz-subtitlefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitletabletfontsize: 20px;--brz-subtitletabletfontweight: 100;--brz-subtitletabletletterspacing: 0px;--brz-subtitletabletlineheight: 1.4;--brz-subtitletabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitlemobilefontsize: 16px;--brz-subtitlemobilefontweight: 100;--brz-subtitlemobileletterspacing: 0px;--brz-subtitlemobilelineheight: 1.4;--brz-subtitlemobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-subtitlestoryfontsize: 5.52%;--brz-subtitlebold: 400;--brz-subtitleitalic: inherit;--brz-subtitletextdecoration: inherit;--brz-subtitletexttransform: inherit;--brz-subtitletabletbold: 100;--brz-subtitletabletitalic: inherit;--brz-subtitletablettextdecoration: inherit;--brz-subtitletablettexttransform: inherit;--brz-subtitlemobilebold: 100;--brz-subtitlemobileitalic: inherit;--brz-subtitlemobiletextdecoration: inherit;--brz-subtitlemobiletexttransform: inherit;--brz-abovetitlefontfamily: "Comfortaa",display;--brz-abovetitlefontsize: 20px;--brz-abovetitlefontsizesuffix: px;--brz-abovetitlefontweight: 400;--brz-abovetitleletterspacing: 0px;--brz-abovetitlelineheight: 1.4;--brz-abovetitlefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitletabletfontsize: 18px;--brz-abovetitletabletfontweight: 400;--brz-abovetitletabletletterspacing: 0px;--brz-abovetitletabletlineheight: 1.4;--brz-abovetitletabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitlemobilefontsize: 18px;--brz-abovetitlemobilefontweight: 400;--brz-abovetitlemobileletterspacing: 0px;--brz-abovetitlemobilelineheight: 1.4;--brz-abovetitlemobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-abovetitlestoryfontsize: 4.6%;--brz-abovetitlebold: 400;--brz-abovetitleitalic: inherit;--brz-abovetitletextdecoration: inherit;--brz-abovetitletexttransform: inherit;--brz-abovetitletabletbold: 400;--brz-abovetitletabletitalic: inherit;--brz-abovetitletablettextdecoration: inherit;--brz-abovetitletablettexttransform: inherit;--brz-abovetitlemobilebold: 400;--brz-abovetitlemobileitalic: inherit;--brz-abovetitlemobiletextdecoration: inherit;--brz-abovetitlemobiletexttransform: inherit;--brz-heading1fontfamily: "Comfortaa",display;--brz-heading1fontsize: 54px;--brz-heading1fontsizesuffix: px;--brz-heading1fontweight: 700;--brz-heading1letterspacing: -.2px;--brz-heading1lineheight: 1.2;--brz-heading1fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1tabletfontsize: 40px;--brz-heading1tabletfontweight: 700;--brz-heading1tabletletterspacing: -.2px;--brz-heading1tabletlineheight: 1.2;--brz-heading1tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1mobilefontsize: 28px;--brz-heading1mobilefontweight: 700;--brz-heading1mobileletterspacing: -.8px;--brz-heading1mobilelineheight: 1.2;--brz-heading1mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading1storyfontsize: 12.42%;--brz-heading1bold: 700;--brz-heading1italic: inherit;--brz-heading1textdecoration: inherit;--brz-heading1texttransform: inherit;--brz-heading1tabletbold: 700;--brz-heading1tabletitalic: inherit;--brz-heading1tablettextdecoration: inherit;--brz-heading1tablettexttransform: inherit;--brz-heading1mobilebold: 700;--brz-heading1mobileitalic: inherit;--brz-heading1mobiletextdecoration: inherit;--brz-heading1mobiletexttransform: inherit;--brz-heading2fontfamily: "Comfortaa",display;--brz-heading2fontsize: 36px;--brz-heading2fontsizesuffix: px;--brz-heading2fontweight: 700;--brz-heading2letterspacing: -.8px;--brz-heading2lineheight: 1.2;--brz-heading2fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2tabletfontsize: 34px;--brz-heading2tabletfontweight: 700;--brz-heading2tabletletterspacing: -.8px;--brz-heading2tabletlineheight: 1.2;--brz-heading2tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2mobilefontsize: 22px;--brz-heading2mobilefontweight: 700;--brz-heading2mobileletterspacing: -1px;--brz-heading2mobilelineheight: 1.2;--brz-heading2mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading2storyfontsize: 8.28%;--brz-heading2bold: 700;--brz-heading2italic: inherit;--brz-heading2textdecoration: inherit;--brz-heading2texttransform: inherit;--brz-heading2tabletbold: 700;--brz-heading2tabletitalic: inherit;--brz-heading2tablettextdecoration: inherit;--brz-heading2tablettexttransform: inherit;--brz-heading2mobilebold: 700;--brz-heading2mobileitalic: inherit;--brz-heading2mobiletextdecoration: inherit;--brz-heading2mobiletexttransform: inherit;--brz-heading3fontfamily: "Comfortaa",display;--brz-heading3fontsize: 22px;--brz-heading3fontsizesuffix: px;--brz-heading3fontweight: 400;--brz-heading3letterspacing: -.6px;--brz-heading3lineheight: 1.4;--brz-heading3fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3tabletfontsize: 28px;--brz-heading3tabletfontweight: 200;--brz-heading3tabletletterspacing: -.2px;--brz-heading3tabletlineheight: 1.3;--brz-heading3tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3mobilefontsize: 20px;--brz-heading3mobilefontweight: 200;--brz-heading3mobileletterspacing: -.8px;--brz-heading3mobilelineheight: 1.3;--brz-heading3mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading3storyfontsize: 5.06%;--brz-heading3bold: 400;--brz-heading3italic: inherit;--brz-heading3textdecoration: inherit;--brz-heading3texttransform: inherit;--brz-heading3tabletbold: 200;--brz-heading3tabletitalic: inherit;--brz-heading3tablettextdecoration: inherit;--brz-heading3tablettexttransform: inherit;--brz-heading3mobilebold: 200;--brz-heading3mobileitalic: inherit;--brz-heading3mobiletextdecoration: inherit;--brz-heading3mobiletexttransform: inherit;--brz-heading4fontfamily: "Comfortaa",display;--brz-heading4fontsize: 18px;--brz-heading4fontsizesuffix: px;--brz-heading4fontweight: 700;--brz-heading4letterspacing: -.2px;--brz-heading4lineheight: 1.4;--brz-heading4fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4tabletfontsize: 17px;--brz-heading4tabletfontweight: 700;--brz-heading4tabletletterspacing: 0px;--brz-heading4tabletlineheight: 1.4;--brz-heading4tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4mobilefontsize: 17px;--brz-heading4mobilefontweight: 700;--brz-heading4mobileletterspacing: 0px;--brz-heading4mobilelineheight: 1.4;--brz-heading4mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading4storyfontsize: 4.14%;--brz-heading4bold: 700;--brz-heading4italic: inherit;--brz-heading4textdecoration: inherit;--brz-heading4texttransform: inherit;--brz-heading4tabletbold: 700;--brz-heading4tabletitalic: inherit;--brz-heading4tablettextdecoration: inherit;--brz-heading4tablettexttransform: inherit;--brz-heading4mobilebold: 700;--brz-heading4mobileitalic: inherit;--brz-heading4mobiletextdecoration: inherit;--brz-heading4mobiletexttransform: inherit;--brz-heading5fontfamily: "Comfortaa",display;--brz-heading5fontsize: 16px;--brz-heading5fontsizesuffix: px;--brz-heading5fontweight: 400;--brz-heading5letterspacing: 0px;--brz-heading5lineheight: 1.4;--brz-heading5fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5tabletfontsize: 16px;--brz-heading5tabletfontweight: 400;--brz-heading5tabletletterspacing: 0px;--brz-heading5tabletlineheight: 1.4;--brz-heading5tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5mobilefontsize: 16px;--brz-heading5mobilefontweight: 400;--brz-heading5mobileletterspacing: 0px;--brz-heading5mobilelineheight: 1.4;--brz-heading5mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading5storyfontsize: 3.68%;--brz-heading5bold: 400;--brz-heading5italic: inherit;--brz-heading5textdecoration: inherit;--brz-heading5texttransform: inherit;--brz-heading5tabletbold: 400;--brz-heading5tabletitalic: inherit;--brz-heading5tablettextdecoration: inherit;--brz-heading5tablettexttransform: inherit;--brz-heading5mobilebold: 400;--brz-heading5mobileitalic: inherit;--brz-heading5mobiletextdecoration: inherit;--brz-heading5mobiletexttransform: inherit;--brz-heading6fontfamily: "Comfortaa",display;--brz-heading6fontsize: 14px;--brz-heading6fontsizesuffix: px;--brz-heading6fontweight: 300;--brz-heading6letterspacing: -.4px;--brz-heading6lineheight: 1.5;--brz-heading6fontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6tabletfontsize: 14px;--brz-heading6tabletfontweight: 400;--brz-heading6tabletletterspacing: 0px;--brz-heading6tabletlineheight: 1.5;--brz-heading6tabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6mobilefontsize: 14px;--brz-heading6mobilefontweight: 400;--brz-heading6mobileletterspacing: 0px;--brz-heading6mobilelineheight: 1.5;--brz-heading6mobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-heading6storyfontsize: 3.22%;--brz-heading6bold: 300;--brz-heading6italic: inherit;--brz-heading6textdecoration: inherit;--brz-heading6texttransform: inherit;--brz-heading6tabletbold: 400;--brz-heading6tabletitalic: inherit;--brz-heading6tablettextdecoration: inherit;--brz-heading6tablettexttransform: inherit;--brz-heading6mobilebold: 400;--brz-heading6mobileitalic: inherit;--brz-heading6mobiletextdecoration: inherit;--brz-heading6mobiletexttransform: inherit;--brz-buttonfontfamily: "Comfortaa",display;--brz-buttonfontsize: 18px;--brz-buttonfontsizesuffix: px;--brz-buttonfontweight: 700;--brz-buttonletterspacing: -.2px;--brz-buttonlineheight: 1.6;--brz-buttonfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttontabletfontsize: 16px;--brz-buttontabletfontweight: 700;--brz-buttontabletletterspacing: 0px;--brz-buttontabletlineheight: 1.6;--brz-buttontabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttonmobilefontsize: 16px;--brz-buttonmobilefontweight: 700;--brz-buttonmobileletterspacing: 0px;--brz-buttonmobilelineheight: 1.6;--brz-buttonmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-buttonstoryfontsize: 4.14%;--brz-buttonbold: 700;--brz-buttonitalic: inherit;--brz-buttontextdecoration: inherit;--brz-buttontexttransform: inherit;--brz-buttontabletbold: 700;--brz-buttontabletitalic: inherit;--brz-buttontablettextdecoration: inherit;--brz-buttontablettexttransform: inherit;--brz-buttonmobilebold: 700;--brz-buttonmobileitalic: inherit;--brz-buttonmobiletextdecoration: inherit;--brz-buttonmobiletexttransform: inherit;--brz-ugudlcdcxlbqfontfamily: "Comfortaa",display;--brz-ugudlcdcxlbqfontsize: 16px;--brz-ugudlcdcxlbqfontsizesuffix: px;--brz-ugudlcdcxlbqfontweight: 400;--brz-ugudlcdcxlbqletterspacing: -.2px;--brz-ugudlcdcxlbqlineheight: 1.4;--brz-ugudlcdcxlbqfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqtabletfontsize: 15px;--brz-ugudlcdcxlbqtabletfontweight: 400;--brz-ugudlcdcxlbqtabletletterspacing: -.2px;--brz-ugudlcdcxlbqtabletlineheight: 1.5;--brz-ugudlcdcxlbqtabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqmobilefontsize: 15px;--brz-ugudlcdcxlbqmobilefontweight: 400;--brz-ugudlcdcxlbqmobileletterspacing: -.2px;--brz-ugudlcdcxlbqmobilelineheight: 1.4;--brz-ugudlcdcxlbqmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-ugudlcdcxlbqstoryfontsize: 3.68%;--brz-ugudlcdcxlbqbold: 400;--brz-ugudlcdcxlbqitalic: inherit;--brz-ugudlcdcxlbqtextdecoration: inherit;--brz-ugudlcdcxlbqtexttransform: inherit;--brz-ugudlcdcxlbqtabletbold: 400;--brz-ugudlcdcxlbqtabletitalic: inherit;--brz-ugudlcdcxlbqtablettextdecoration: inherit;--brz-ugudlcdcxlbqtablettexttransform: inherit;--brz-ugudlcdcxlbqmobilebold: 400;--brz-ugudlcdcxlbqmobileitalic: inherit;--brz-ugudlcdcxlbqmobiletextdecoration: inherit;--brz-ugudlcdcxlbqmobiletexttransform: inherit;--brz-u9xihr8qxhssfontfamily: "Comfortaa",display;--brz-u9xihr8qxhssfontsize: 14px;--brz-u9xihr8qxhssfontsizesuffix: px;--brz-u9xihr8qxhssfontweight: 700;--brz-u9xihr8qxhssletterspacing: -.2px;--brz-u9xihr8qxhsslineheight: 1.4;--brz-u9xihr8qxhssfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhsstabletfontsize: 14px;--brz-u9xihr8qxhsstabletfontweight: 400;--brz-u9xihr8qxhsstabletletterspacing: -.3px;--brz-u9xihr8qxhsstabletlineheight: 1.2;--brz-u9xihr8qxhsstabletfontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhssmobilefontsize: 13px;--brz-u9xihr8qxhssmobilefontweight: 700;--brz-u9xihr8qxhssmobileletterspacing: -.4px;--brz-u9xihr8qxhssmobilelineheight: 1.3;--brz-u9xihr8qxhssmobilefontvariation: "wght" 400,"wdth" 100,"SOFT" 0;--brz-u9xihr8qxhssstoryfontsize: 3.22%;--brz-u9xihr8qxhssbold: 700;--brz-u9xihr8qxhssitalic: inherit;--brz-u9xihr8qxhsstextdecoration: inherit;--brz-u9xihr8qxhsstexttransform: inherit;--brz-u9xihr8qxhsstabletbold: 400;--brz-u9xihr8qxhsstabletitalic: inherit;--brz-u9xihr8qxhsstablettextdecoration: inherit;--brz-u9xihr8qxhsstablettexttransform: inherit;--brz-u9xihr8qxhssmobilebold: 700;--brz-u9xihr8qxhssmobileitalic: inherit;--brz-u9xihr8qxhssmobiletextdecoration: inherit;--brz-u9xihr8qxhssmobiletexttransform: inherit;}</style><script src="assets/79ee0aed42e5d5739dfd3e832fd956d3.js" class="brz-script brz-script-preview-lib" defer="true" data-brz-group="group-1_3"></script><script src="assets/7bd5bb61c3a6cf37aef2a8ce02be7461.js" class="brz-script brz-script-preview-lib-pro" defer="true" data-brz-group="group-1_2"></script><script src="assets/246b62aec7f6f35cef79e957ee00b9c9.js" class="brz-script brz-script-preview-pro" defer="true"></script></head><body class="brz">        <div class="brz-root__container brz-reset-all brz brz-root__container-page"><section id="ie37fcd27a75a13547285_add3583050b76c25118a9b567f3238c29" class="brz-section brz-section__header brz-css-sk5zf3 brz-css-c8fq4o"><div class="brz-section__menu-item brz-css-1wzn4p1 brz-css-1uopts9" data-brz-custom-id="pmjtztsvmmgucjewowvarmptbegnhqkrygyg"><div class="brz-container brz-css-2gpbzd brz-css-1efqzy4"><header class="brz-row__container brz-css-1t66ezt brz-css-1a7ogzm" data-brz-custom-id="qxrqcghhpyzdntkwxqtwalcnjpphwfwfpnai"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-row brz-css-lt4404 brz-css-18oneyz"><div class="brz-columns brz-css-1csnpdv brz-css-88dicl" data-brz-custom-id="mxbfnikhdyxqeblxvgyysleuexbinafgdwmy"><div class="brz-column__items brz-css-1pzte4r brz-css-znagm0"><div id="" class="brz-css-1tjbyob brz-css-1puqgto brz-wrapper"><div class="brz-image brz-css-q31jzm brz-css-yp9xav" data-brz-custom-id="ozphomskbadqfdvikpfdqyfniixzvkbtxcbk"><a class="brz-a" href="/" target="_self" rel="noopener" data-brz-link-type="external"><picture class="brz-picture brz-d-block brz-p-relative brz-css-1yykq65 brz-css-z4zlrs"><source srcset="assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" media="(min-width: 992px)"><source srcset="assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" media="(min-width: 768px)"><img class="brz-img" srcset="assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" src="assets/5bd10bf04072b5d66d2acac718fe483e.webp" alt="" title="cymta-logo-el.webp" draggable="false" loading="lazy"></picture></a></div></div></div></div><div class="brz-columns brz-css-1csnpdv brz-css-1t2tmqk" data-brz-custom-id="lvqsghryjlhtilcubdlpnhcdhvaftslruozx"><div class="brz-column__items brz-css-1pzte4r brz-css-kmg040"><div id="" class="brz-css-1tjbyob brz-css-icg1tg brz-wrapper"><div class="brz-menu__container brz-css-19uzdaw brz-css-159oqwi" data-mmenu-id="#fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw_i480cd0d8e7904b7c1345" data-mmenu-position="position-left" data-mmenu-title="Menu" data-mmenu-stickytitle="on" data-mmenu-closingicon="%7B%22desktop%22%3A%22off%22%2C%22tablet%22%3A%22off%22%2C%22mobile%22%3A%22off%22%7D" data-brz-custom-id="fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw"><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" class="brz-menu brz-menu__preview brz-css-c39cel brz-css-1tdvdmo"><ul class="brz-menu__ul"><li data-menu-item-id="b22b960322341a5e734d1324bbd76ed2" class="brz-menu__item"><a class="brz-a" target="" href="/mt" title="Μουσικοθεραπεία"><span class="brz-span">Μουσικοθεραπεία</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="01d48bae25f7229912cdc6bd654489a6" class="brz-menu__item"><a class="brz-a" target="" href="/find" title="Βρες Μουσικοθεραπευτή"><span class="brz-span">Βρες Μουσικοθεραπευτή</span></a></li><li data-menu-item-id="ead41e4a9b5e03ba07aeb9e426f4c21a" class="brz-menu__item"><a class="brz-a" target="" href="/study" title="Σπουδές Μουσικοθεραπείας"><span class="brz-span">Σπουδές Μουσικοθεραπείας</span></a></li><li data-menu-item-id="602acc623566a539fa600e1a24add4a0" class="brz-menu__item"><a class="brz-a" target="" href="/mt-info" title="Πληροφορίες για Μουσικοθεραπευτές"><span class="brz-span">Πληροφορίες για Μουσικοθεραπευτές</span></a></li></ul></li><li data-menu-item-id="3e349ad76a29de800ba11951eee69b60" class="brz-menu__item brz-menu__item--current"><a class="brz-a" target="" href="mt-blog.html" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="ccf214e2ed986735e372c15ea2c09523" class="brz-menu__item"><a class="brz-a" target="" href="/contact" title="Επικοινωνία"><span class="brz-span">Επικοινωνία</span></a></li></ul></nav><div class="brz-mm-menu__icon"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/8e85539e41ed2686da0dcff2d601af13.svg#brz_icon"></use></svg></div><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" id="fbiyjwwellhvtbfzvawrysdglhnvhbgmljyw_i480cd0d8e7904b7c1345" class="brz-menu brz-menu__preview brz-menu__mmenu brz-menu--has-dropdown brz-css-12dn42o brz-css-1x5bkh9"><ul class="brz-menu__ul"><li data-menu-item-id="b22b960322341a5e734d1324bbd76ed2" class="brz-menu__item"><a class="brz-a" target="" href="/mt" title="Μουσικοθεραπεία"><span class="brz-span">Μουσικοθεραπεία</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="01d48bae25f7229912cdc6bd654489a6" class="brz-menu__item"><a class="brz-a" target="" href="/find" title="Βρες Μουσικοθεραπευτή"><span class="brz-span">Βρες Μουσικοθεραπευτή</span></a></li><li data-menu-item-id="ead41e4a9b5e03ba07aeb9e426f4c21a" class="brz-menu__item"><a class="brz-a" target="" href="/study" title="Σπουδές Μουσικοθεραπείας"><span class="brz-span">Σπουδές Μουσικοθεραπείας</span></a></li><li data-menu-item-id="602acc623566a539fa600e1a24add4a0" class="brz-menu__item"><a class="brz-a" target="" href="/mt-info" title="Πληροφορίες για Μουσικοθεραπευτές"><span class="brz-span">Πληροφορίες για Μουσικοθεραπευτές</span></a></li></ul></li><li data-menu-item-id="3e349ad76a29de800ba11951eee69b60" class="brz-menu__item brz-menu__item--current"><a class="brz-a" target="" href="mt-blog.html" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="ccf214e2ed986735e372c15ea2c09523" class="brz-menu__item"><a class="brz-a" target="" href="/contact" title="Επικοινωνία"><span class="brz-span">Επικοινωνία</span></a></li></ul></nav></div></div><div id="" class="brz-css-1tjbyob brz-css-19ioiov brz-wrapper"><div class="brz-menu__container brz-css-19uzdaw brz-css-1mxc0dk" data-mmenu-id="#tkCzeDJsTpP8_i480cd0d8e7904b7c1345" data-mmenu-position="position-left" data-mmenu-title="" data-mmenu-stickytitle="on" data-mmenu-closingicon="%7B%22desktop%22%3A%22off%22%2C%22tablet%22%3A%22off%22%2C%22mobile%22%3A%22off%22%7D" data-brz-custom-id="tkCzeDJsTpP8"><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" class="brz-menu brz-menu__preview brz-css-c39cel brz-css-1dgf3el"><ul class="brz-menu__ul"><li data-menu-item-id="68498357bd9751cca09ce585b751e087" class="brz-menu__item"><a class="brz-a" target="" href="/mt" title="Μουσικοθεραπεία"><span class="brz-span">Μουσικοθεραπεία</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="75d4d1dc5b0c65580e43666ccee4f7a7" class="brz-menu__item"><a class="brz-a" target="" href="/find" title="Βρες Μουσικοθεραπευτή"><span class="brz-span">Βρες Μουσικοθεραπευτή</span></a></li><li data-menu-item-id="8b426b54feb315f34ab6402a783f37ea" class="brz-menu__item"><a class="brz-a" target="" href="/study" title="Σπουδές Μουσικοθεραπείας"><span class="brz-span">Σπουδές Μουσικοθεραπείας</span></a></li><li data-menu-item-id="079c12ac444e6db44d0c1aef67233f3b" class="brz-menu__item"><a class="brz-a" target="" href="/mt-info" title="Πληροφορίες για Μουσικοθεραπευτές"><span class="brz-span">Πληροφορίες για Μουσικοθεραπευτές</span></a></li></ul></li><li data-menu-item-id="54aaa9e0763f70d42976f10caca42b9e" class="brz-menu__item brz-menu__item--current"><a class="brz-a" target="" href="mt-blog.html" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="8db2f3adb442538b322f3a1e9822fb29" class="brz-menu__item"><a class="brz-a" target="" href="/contact" title="Επικοινωνία"><span class="brz-span">Επικοινωνία</span></a></li><li data-menu-item-id="0c5b64c5539a611a193d2ef63578b507" class="brz-menu__item"><a class="brz-a" target="" href="/en" title="EN"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/83309393473f0d678e4bb3660c2ccfea.svg#nc_icon"></use></svg><span class="brz-span">EN</span></a></li></ul></nav><div class="brz-mm-menu__icon"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/8e85539e41ed2686da0dcff2d601af13.svg#brz_icon"></use></svg></div><nav data-mods="%7B%22desktop%22%3A%22horizontal%22%2C%22tablet%22%3A%22horizontal%22%2C%22mobile%22%3A%22horizontal%22%7D" id="tkCzeDJsTpP8_i480cd0d8e7904b7c1345" class="brz-menu brz-menu__preview brz-menu__mmenu brz-menu--has-dropdown brz-css-12dn42o brz-css-1kwae1d"><ul class="brz-menu__ul"><li data-menu-item-id="68498357bd9751cca09ce585b751e087" class="brz-menu__item"><a class="brz-a" target="" href="/mt" title="Μουσικοθεραπεία"><span class="brz-span">Μουσικοθεραπεία</span></a><ul class="brz-menu__sub-menu"><li data-menu-item-id="75d4d1dc5b0c65580e43666ccee4f7a7" class="brz-menu__item"><a class="brz-a" target="" href="/find" title="Βρες Μουσικοθεραπευτή"><span class="brz-span">Βρες Μουσικοθεραπευτή</span></a></li><li data-menu-item-id="8b426b54feb315f34ab6402a783f37ea" class="brz-menu__item"><a class="brz-a" target="" href="/study" title="Σπουδές Μουσικοθεραπείας"><span class="brz-span">Σπουδές Μουσικοθεραπείας</span></a></li><li data-menu-item-id="079c12ac444e6db44d0c1aef67233f3b" class="brz-menu__item"><a class="brz-a" target="" href="/mt-info" title="Πληροφορίες για Μουσικοθεραπευτές"><span class="brz-span">Πληροφορίες για Μουσικοθεραπευτές</span></a></li></ul></li><li data-menu-item-id="54aaa9e0763f70d42976f10caca42b9e" class="brz-menu__item brz-menu__item--current"><a class="brz-a" target="" href="mt-blog.html" title="Blog"><span class="brz-span">Blog</span></a></li><li data-menu-item-id="8db2f3adb442538b322f3a1e9822fb29" class="brz-menu__item"><a class="brz-a" target="" href="/contact" title="Επικοινωνία"><span class="brz-span">Επικοινωνία</span></a></li><li data-menu-item-id="0c5b64c5539a611a193d2ef63578b507" class="brz-menu__item"><a class="brz-a" target="" href="/en" title="EN"><svg class="brz-icon-svg align-[initial] brz-mm-menu__item__icon"><use href="assets/svg/83309393473f0d678e4bb3660c2ccfea.svg#nc_icon"></use></svg><span class="brz-span">EN</span></a></li></ul></nav></div></div></div></div><div id="translatorclick" class="brz-columns brz-css-1csnpdv brz-css-jow5dd" data-brz-custom-id="ofxnnpp8wVND"><div class="brz-column__items brz-css-1pzte4r brz-css-190lc1z"><div id="" class="brz-css-1tjbyob brz-css-xmdiqp brz-wrapper"><div class="brz-wrapper-transform"><div class="brz-icon-text brz-css-nntapz brz-css-1umndxb" data-brz-custom-id="gsFk4ZWbjT9L"><div class="brz-icon__container" data-brz-custom-id="oNVhNf89QSOh"><span class="brz-icon brz-span brz-css-e9bk1k brz-css-h3q0h5"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/673cf948d20838cde5e7c8f64474298f.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-10fnxcx brz-css-1n6htfm" data-brz-custom-id="k7MTYkg143MN"><div data-brz-translate-text="1"><p class="brz-mt-lg-0 brz-text-lg-justify brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-lg-empty brz-ff-comfortaa brz-ft-google brz-fs-lg-16 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-m_0_2 brz-lh-lg-1_7 brz-css-of8RQ" data-uniq-id="yAzBt" data-generated-css="brz-css-wO7DX"><span style="color: rgba(var(--brz-global-color7),1);" class="brz-cp-color7">EN</span></p></div></div></div></div></div></div><div id="" class="brz-css-1tjbyob brz-css-r8i1ey brz-wrapper"><div class="brz-embed-code brz-css-zwba1n brz-css-3bcg12" data-brz-custom-id="dkIB9yIrtPL_"><div class="brz-embed-content"><div><style>#translatorclick {transition: transform .3s ease-in-out;}
#translatorclick:hover {transform: scale(1.08);}</style></div></div></div></div></div><a class="brz-a brz-container-link" href="/en" target="_self" rel="noopener" data-brz-link-type="external"></a></div></div></header></div></div></section>     <section id="if2c99376773f5fd03e5e_acoilykiizkqhgwqbvfqffavspclprblutte" class="brz-section brz-css-q1tgtc brz-css-9fvlif"><div class="brz-section__content brz-section--boxed brz-css-7pdknx brz-css-19bezg3" data-brz-custom-id="nyxialvskkrxtuhbrcurqbfrjlbrrltrgmpj"><div class="brz-bg"><div class="brz-bg-image"></div><div class="brz-bg-color"></div></div><div class="brz-container brz-css-1ap2zsh brz-css-g3ty65"><div id="" class="brz-css-14xvecl brz-css-hio4wl brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-1xd4var" data-brz-custom-id="gepaqktygnnrzdrwfoplebrdcvwmbsjuukwr"><div data-brz-translate-text="1"><h1 class="brz-text-lg-center brz-tp-lg-heading1 brz-css-xQy3I" data-uniq-id="i81__" data-generated-css="brz-css-zf0bt"><span style="color: rgba(var(--brz-global-color8),1); text-shadow: rgb(0, 0, 0) 1px 2px 8px;" class="brz-cp-color8">Blog / Άρθρα</span></h1></div></div></div></div></div></section><section id="i378ccdd90810f9271ad2_ghmbolnxkxsrbevniodolvbsmkhebopscbrb" class="brz-section brz-css-q1tgtc brz-css-1njd3gj"><div class="brz-section__content brz-section--boxed brz-css-7pdknx brz-css-mxpdf1" data-brz-custom-id="xtsjkdeehywsmnrweynyfaafxogqhomspjld"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-container brz-css-1ap2zsh brz-css-2g5ayj"><div class="brz-row__container brz-css-1ylkdyc brz-css-1ss6de9" data-brz-custom-id="xhqfbyaadlmbwsncxibrkclagjjafjvxnpuz"><div class="brz-row brz-css-bwj1w brz-css-dj0192 brz-css-1y28zjb"><div class="brz-columns brz-css-jbjqyi brz-css-1s8u9cv" data-brz-custom-id="bgetyesyfebawfudntzfcxddzsibwgwvgxit"><div class="brz-column__items brz-css-571mf8 brz-css-qrjbxt"><div class="brz-row__container brz-css-1ylkdyc brz-css-37d1g5" data-brz-custom-id="vhdtkmbgpnmwiandazqwphshvtdyabfvsbty"><div class="brz-row brz-row--inner brz-css-bwj1w brz-css-dj0192 brz-css-5ecxbm"><div data-brz-iteration-count="1" class="brz-columns brz-css-jbjqyi brz-css-1hmd296 brz-animated brz-css-1dam7wr brz-css-1v3th81" data-brz-custom-id="pgxgxkhbcbwcxdvbeqdtglclfcsdxhguiblf"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-column__items brz-css-571mf8 brz-css-cvpvfe"><div id="" class="brz-css-14xvecl brz-css-flz70m brz-wrapper"><div class="brz-image brz-css-16faig1 brz-css-8x7zlp" data-brz-custom-id="bhNeuXMvD2Gx"><picture class="brz-picture brz-d-block brz-p-relative brz-css-g279k7 brz-css-1ly5fak"><source srcset="assets/a124cedb8798b7a834f953dab3d05d2a.webp 1x, assets/a124cedb8798b7a834f953dab3d05d2a.webp 2x" media="(min-width: 992px)"><source srcset="assets/a124cedb8798b7a834f953dab3d05d2a.webp 1x, assets/a124cedb8798b7a834f953dab3d05d2a.webp 2x" media="(min-width: 768px)"><img class="brz-img" srcset="assets/a124cedb8798b7a834f953dab3d05d2a.webp 1x, assets/a124cedb8798b7a834f953dab3d05d2a.webp 2x" src="assets/a124cedb8798b7a834f953dab3d05d2a.webp" alt="" title="piano-child-mt.webp" draggable="false" loading="lazy"></picture></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-1yowkui brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-7mq8k"></div></div><div class="brz-row__container brz-css-1ylkdyc brz-css-ah6ygt" data-brz-custom-id="gckulstmokzsqjevikdgblytodqkmmwagtdp"><div class="brz-row brz-row--inner brz-css-bwj1w brz-css-dj0192 brz-css-12b0c4u"><div class="brz-columns brz-css-jbjqyi brz-css-10r2txj" data-brz-custom-id="najefsqxtqhbjqbjfwetpkgozmazvkrigxnq"><div class="brz-bg"></div><div class="brz-column__items brz-css-571mf8 brz-css-1hlehkh"><div id="" class="brz-css-14xvecl brz-css-1p8qt26 brz-wrapper"><div class="brz-icon-text brz-css-1s68ja brz-css-1vz21gu" data-brz-custom-id="mdcyirzwihbcfuqjwbepackuqopbisnpzunl"><div class="brz-icon__container" data-brz-custom-id="odniqrhinvrykwbkyrzwxqezpbaxcsuovxfr"><span class="brz-icon brz-span brz-css-13audko brz-css-12920um"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/fdab52894e524cc8dcef2be73e41b809.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-1270wrn" data-brz-custom-id="ylrdqaiohhowychnsbepcpmbgouzlzszvgie"><div data-brz-translate-text="1"><p class="brz-tp-lg-heading6 brz-tp-xs-empty brz-fs-xs-13 brz-fss-xs-px brz-fw-xs-400 brz-ls-xs-0 brz-lh-xs-1_5 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-tp-sm-empty brz-fs-sm-13 brz-fss-sm-px brz-fw-sm-400 brz-ls-sm-0 brz-lh-sm-1_5 brz-vfw-sm-400 brz-fwdth-sm-100 brz-fsft-sm-0 brz-css-ea64F" data-generated-css="brz-css-cYZ97" data-uniq-id="buExd"><span class="brz-cp-color7">Αντωνία Πλυσή</span></p></div></div></div></div></div></div></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-1jm5elg brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-4yqsm6"></div></div><div id="" class="brz-css-14xvecl brz-css-1ef4wpt brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-9rdqdb" data-brz-custom-id="ozlmvkdqkrfnsyurlsaxygkrzgtuhvwcbwjs"><div data-brz-translate-text="1"><p class="brz-tp-lg-heading4 brz-text-lg-left brz-css-hx9_H" data-uniq-id="ina8k" data-generated-css="brz-css-trnXQ"><span class="brz-cp-color2">Ξεχωριστές στιγμές αμοιβαιότητας στη Μουσικοθεραπεία, με ένα παιδί με αυτισμό</span></p></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-16mk6t1 brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-1dlpx7r"></div></div><div id="" class="brz-css-14xvecl brz-css-1y66mil brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-11vio22" data-brz-custom-id="sitwxvysgkamnexnpencnvcpbtiamdqopoht"><div data-brz-translate-text="1"><p class="brz-text-lg-left brz-tp-sm-uGuDLCdCXLbq brz-tp-lg-uGuDLCdCXLbq brz-css-mGhV0" data-generated-css="brz-css-sbNiW" data-uniq-id="klljh"><span class="brz-cp-color7">Η Μαρία ήταν τριών χρονών όταν πρωτοήλθε για Μουσικοθεραπεία. Διαγνωσμένη με αυτισμό, είχε δυσκολίες στην επικοινωνία και κοινωνική αλληλεπίδραση.</span></p></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-chidof brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-jzezgo"></div></div></div><a class="brz-a brz-container-link" href="/mt-special-moments" target="_self" rel="noopener" data-brz-link-type="external"></a></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-17wbs37 brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-uvrsr"></div></div><div class="brz-row__container brz-css-1ylkdyc brz-css-o1xx0d" data-brz-custom-id="baoaiprspcgfyvlqtdywwayyhajdzebsonbx"><div class="brz-row brz-row--inner brz-css-bwj1w brz-css-dj0192 brz-css-a4whaz"><div data-brz-iteration-count="1" class="brz-columns brz-css-jbjqyi brz-css-11gfgi5 brz-animated brz-css-1dam7wr brz-css-ys064w" data-brz-custom-id="avvF5t9EUKRW"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-column__items brz-css-571mf8 brz-css-1hc8j29"><div id="" class="brz-css-14xvecl brz-css-4dqojl brz-wrapper"><div class="brz-image brz-css-62k2cq brz-css-1da5w5f" data-brz-custom-id="ffQSVrgi2itq"><picture class="brz-picture brz-d-block brz-p-relative brz-css-1prllfz brz-css-1gyf6fj"><source srcset="assets/7edfb547e31be8bb8d886361349c1543.webp 1x, assets/7edfb547e31be8bb8d886361349c1543.webp 2x" media="(min-width: 992px)"><source srcset="assets/7edfb547e31be8bb8d886361349c1543.webp 1x, assets/7edfb547e31be8bb8d886361349c1543.webp 2x" media="(min-width: 768px)"><img class="brz-img" srcset="assets/7edfb547e31be8bb8d886361349c1543.webp 1x, assets/7edfb547e31be8bb8d886361349c1543.webp 2x" src="assets/7edfb547e31be8bb8d886361349c1543.webp" alt="" title="guitar-child-mt.webp" draggable="false" loading="lazy"></picture></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-z17p2e brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-wpk51k"></div></div><div class="brz-row__container brz-css-1ylkdyc brz-css-vx1gfn" data-brz-custom-id="hh04p4qDAhCi"><div class="brz-row brz-row--inner brz-css-bwj1w brz-css-dj0192 brz-css-tiodmh"><div class="brz-columns brz-css-jbjqyi brz-css-oa95ur" data-brz-custom-id="fzQum2hVduqS"><div class="brz-bg"></div><div class="brz-column__items brz-css-571mf8 brz-css-jw81xq"><div id="" class="brz-css-14xvecl brz-css-9172he brz-wrapper"><div class="brz-icon-text brz-css-1s68ja brz-css-1q94uue" data-brz-custom-id="eMGsuJ_aM8i3"><div class="brz-icon__container" data-brz-custom-id="fZmletauKHHp"><span class="brz-icon brz-span brz-css-13audko brz-css-mp2kc5"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/fdab52894e524cc8dcef2be73e41b809.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-ni65aj" data-brz-custom-id="yN2XbIzIWOYF"><div data-brz-translate-text="1"><p class="brz-tp-lg-heading6 brz-tp-xs-empty brz-fs-xs-13 brz-fss-xs-px brz-fw-xs-400 brz-ls-xs-0 brz-lh-xs-1_5 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-tp-sm-empty brz-fs-sm-13 brz-fss-sm-px brz-fw-sm-400 brz-ls-sm-0 brz-lh-sm-1_5 brz-vfw-sm-400 brz-fwdth-sm-100 brz-fsft-sm-0 brz-css-hvV0r" data-uniq-id="ufJry" data-generated-css="brz-css-dRYk2"><span class="brz-cp-color7" style="color: rgb(103, 115, 108);">Αντωνία Χατζηευτυχίου </span></p></div></div></div></div></div></div></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-box8a8 brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-t8nrzi"></div></div><div id="" class="brz-css-14xvecl brz-css-mhx9ru brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-fdqqzl" data-brz-custom-id="kqEcXMie3LI8"><div data-brz-translate-text="1"><p class="brz-text-lg-left brz-tp-lg-heading4 brz-css-uJQps" data-generated-css="brz-css-a_VXH" data-uniq-id="khzz9"><span class="brz-cp-color2">Μουσικοθεραπεία και Αυτισμός</span></p></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-11uhh8p brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-1a2bbvn"></div></div><div id="" class="brz-css-14xvecl brz-css-u8ge0l brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-wzl5qk" data-brz-custom-id="hDnglX8qVYv8"><div data-brz-translate-text="1"><p class="brz-tp-lg-uGuDLCdCXLbq brz-tp-sm-uGuDLCdCXLbq brz-text-lg-left brz-css-sGcTv" data-uniq-id="tMIge" data-generated-css="brz-css-g28WX"><span class="brz-cp-color7">Σήμερα, η μουσικοθεραπεία είναι αναγνωρισμένη ως μια αποτελεσματική παρέμβαση για τα άτομα με Διαταραχή Αυτιστικού Φάσματος (Autism Spectrum Disorder) ή πιο απλά με αυτισμό.</span></p></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-tdj4vu brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-1jm4twr"></div></div></div><a class="brz-a brz-container-link" href="/mt-autism" target="_self" rel="noopener" data-brz-link-type="external"></a></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-3pcm9i brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-11wro6i"></div></div><div class="brz-row__container brz-css-1ylkdyc brz-css-178od83" data-brz-custom-id="fkazqtjhltybqpyqaaxfplfypbelwvcfrgwh"><div class="brz-row brz-row--inner brz-css-bwj1w brz-css-dj0192 brz-css-1bbj3y6"><div data-brz-iteration-count="1" class="brz-columns brz-css-jbjqyi brz-css-1arm5on brz-animated brz-css-1dam7wr brz-css-505i9y" data-brz-custom-id="nWZLNFoClA3r"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-column__items brz-css-571mf8 brz-css-3q6k7k"><div id="" class="brz-css-14xvecl brz-css-1578od2 brz-wrapper"><div class="brz-image brz-css-9q35a3 brz-css-jbtve9" data-brz-custom-id="b9QAqloW30dY"><picture class="brz-picture brz-d-block brz-p-relative brz-css-1se2trw brz-css-evx8cc"><source srcset="assets/d08b545a0e46ef7a48a83d868df3c7f3.webp 1x, assets/d08b545a0e46ef7a48a83d868df3c7f3.webp 2x" media="(min-width: 992px)"><source srcset="assets/d08b545a0e46ef7a48a83d868df3c7f3.webp 1x, assets/d08b545a0e46ef7a48a83d868df3c7f3.webp 2x" media="(min-width: 768px)"><img class="brz-img" srcset="assets/d08b545a0e46ef7a48a83d868df3c7f3.webp 1x, assets/d08b545a0e46ef7a48a83d868df3c7f3.webp 2x" src="assets/d08b545a0e46ef7a48a83d868df3c7f3.webp" alt="" title="faq--1739268956077.webp" draggable="false" loading="lazy"></picture></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-17njfa brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-1e1f4n2"></div></div><div class="brz-row__container brz-css-1ylkdyc brz-css-16i5hfg" data-brz-custom-id="pzuaLkySTh8m"><div class="brz-row brz-row--inner brz-css-bwj1w brz-css-dj0192 brz-css-1dauug0"><div class="brz-columns brz-css-jbjqyi brz-css-xfh61" data-brz-custom-id="lREXSnjVZLL0"><div class="brz-bg"></div><div class="brz-column__items brz-css-571mf8 brz-css-ho80ro"><div id="" class="brz-css-14xvecl brz-css-zo0y1d brz-wrapper"><div class="brz-icon-text brz-css-1s68ja brz-css-1vaqlfe" data-brz-custom-id="undQORZUgyaR"><div class="brz-icon__container" data-brz-custom-id="tL6CdK6djeou"><span class="brz-icon brz-span brz-css-13audko brz-css-lx14r1"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/fdab52894e524cc8dcef2be73e41b809.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-dkur6m" data-brz-custom-id="uqxu_oGpC7r_"><div data-brz-translate-text="1"><p class="brz-fsft-sm-0 brz-fwdth-sm-100 brz-vfw-sm-400 brz-lh-sm-1_5 brz-ls-sm-0 brz-fw-sm-400 brz-fss-sm-px brz-fs-sm-13 brz-tp-sm-empty brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_5 brz-ls-xs-0 brz-fw-xs-400 brz-fss-xs-px brz-fs-xs-13 brz-tp-xs-empty brz-tp-lg-heading6 brz-css-lM3aQ" data-uniq-id="lrkgZ" data-generated-css="brz-css-xzrhd"><span class="brz-cp-color7">Σύλλογος Εγγεγραμμένων Μουσικοθεραπευτών Κύπρου</span></p></div></div></div></div></div></div></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-rhzr3n brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-1ypxozs"></div></div><div id="" class="brz-css-14xvecl brz-css-zd5emd brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-zr3nij" data-brz-custom-id="kaZbGIJayN61"><div data-brz-translate-text="1"><p class="brz-text-lg-left brz-tp-lg-heading4 brz-css-qBZrA" data-generated-css="brz-css-a8P3s" data-uniq-id="pDJS2"><span class="brz-cp-color2">Συχνές Ερωτήσεις - Frequently Asked Questions</span></p></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-3uvscw brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-9jr3en"></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-1e3kb35 brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-1f7fn3g"></div></div></div><a class="brz-a brz-container-link" href="/faq" target="_self" rel="noopener" data-brz-link-type="external"></a></div></div></div></div></div><div data-brz-iteration-count="1" class="brz-columns brz-css-jbjqyi brz-css-438ujj brz-animated brz-css-1dam7wr brz-css-nsyg99" data-brz-custom-id="gawjyauvoxjzkgfydvowwcgvonrwzalrbrli"><div class="brz-column__items brz-css-571mf8 brz-css-1tt9xs1"><div class="brz-row__container brz-css-1ylkdyc brz-css-1hhnxy9" data-brz-custom-id="dxmhawslfzdrwvjjofnscikrcnzeehkispkg"><div class="brz-row brz-row--inner brz-css-bwj1w brz-css-dj0192 brz-css-e8rkn7"><div class="brz-columns brz-css-jbjqyi brz-css-14b2gl5" data-brz-custom-id="knyolmzeubczdezssesclpaoblvufvsxiuvx"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-column__items brz-css-571mf8 brz-css-178zsb6"><div id="" class="brz-css-14xvecl brz-css-hbh2p3 brz-wrapper"><div class="brz-image brz-css-l7g90s brz-css-b7hy5k" data-brz-custom-id="b74KavlibxGH"><picture class="brz-picture brz-d-block brz-p-relative brz-css-14b2s53 brz-css-1xsdico"><source srcset="assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" media="(min-width: 992px)"><source srcset="assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" media="(min-width: 768px)"><img class="brz-img" srcset="assets/5bd10bf04072b5d66d2acac718fe483e.webp 1x, assets/5bd10bf04072b5d66d2acac718fe483e.webp 2x" src="assets/5bd10bf04072b5d66d2acac718fe483e.webp" alt="" title="cymta-logo-el.webp" draggable="false" loading="lazy"></picture></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-1xrohhk brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-oanq0w"></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1a6mrug brz-css-1wzokml" data-brz-custom-id="auctpsudsgsbmibjlqnbytnsxhtgncdwkqoz"><a class="brz-a brz-btn brz-css-1qvgjon brz-css-4gtoyy brz-css-1qq0lr1 brz-css-1s6287f" href="/contact" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="sxzacglpdrslviigfjmyjybauoxddbltqqfk"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Επικοινωνία</span></a></div></div></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-9o72yj brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-kz09d7"></div></div><div class="brz-row__container brz-css-1ylkdyc brz-css-1ijr2n9" data-brz-custom-id="jxgscqmbxpsdtpoacxpgmjrtgoxurldmplrt"><div class="brz-row brz-row--inner brz-css-bwj1w brz-css-dj0192 brz-css-11u2wkc"><div class="brz-columns brz-css-jbjqyi brz-css-2m4d3p" data-brz-custom-id="fpwgjnvcrqtlaexiveulgnltgxixglxwaubf"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-column__items brz-css-571mf8 brz-css-1p1v8an"><div id="" class="brz-css-14xvecl brz-css-1gsjv57 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-18p45dg brz-css-1dk8ecp" data-brz-custom-id="xmyvorlnwzwffglwsbnbqyvvfgubpdqaqgsb"><div data-brz-translate-text="1"><h4 data-generated-css="brz-css-jVM2m" data-uniq-id="iAcEg" class="brz-text-lg-left brz-tp-lg-heading4 brz-css-e5FrC"><span class="brz-cp-color2">Εξερευνήστε</span></h4></div></div></div><div id="" class="brz-css-14xvecl brz-css-5syyht brz-css-1eu54zn brz-wrapper"><div class="brz-spacer brz-css-z4dbzf brz-css-14jz63z"></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1a6mrug brz-css-1ji8hx9" data-brz-custom-id="csytjqmuipwzgtasabfmjmanrrjeqaezjffp"><a class="brz-a brz-btn brz-css-1qvgjon brz-css-wgtz9l brz-css-1qq0lr1 brz-css-14u4wrw" href="mt" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="nhwhndlehlerzwxszmyoztwftqbdcevcdljy"><svg class="brz-icon-svg align-[initial] brz-css-8l4ene brz-css-w0eyde"><use href="assets/svg/27491414ce19b6aef50d991f600a4d39.svg#nc_icon"></use></svg><span data-brz-translate-text="1" class="brz-span brz-text__editor">Μουσικοθεραπεία</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1a6mrug brz-css-1rcxp83" data-brz-custom-id="esellsviecgdibtdtxshoquxbekthkemxymp"><a class="brz-a brz-btn brz-css-1qvgjon brz-css-1atgt3r brz-css-1qq0lr1 brz-css-1nr5xej" href="find" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="bvqhwqovpkdkdyrqtydjcuuemxkadxfudtbl"><svg class="brz-icon-svg align-[initial] brz-css-8l4ene brz-css-1mtlihm"><use href="assets/svg/27491414ce19b6aef50d991f600a4d39.svg#nc_icon"></use></svg><span data-brz-translate-text="1" class="brz-span brz-text__editor">Βρες Μουσικοθεραπευτή</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1a6mrug brz-css-yjdjoj" data-brz-custom-id="ilmxcsqffjdhsfvmynftcdytuehwyhxfpnnz"><a class="brz-a brz-btn brz-css-1qvgjon brz-css-q3e05q brz-css-1qq0lr1 brz-css-17suvs3" href="study" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="hsmlhwzcmrfuboxroovmelbdwvuxqxbunvzi"><svg class="brz-icon-svg align-[initial] brz-css-8l4ene brz-css-qumphi"><use href="assets/svg/27491414ce19b6aef50d991f600a4d39.svg#nc_icon"></use></svg><span data-brz-translate-text="1" class="brz-span brz-text__editor">Σπουδές Μουσικοθεραπείας</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-1a6mrug brz-css-1lvtruc" data-brz-custom-id="eheuteflmupuwatldhxnkrsrzbkattadpkzh"><a class="brz-a brz-btn brz-css-1qvgjon brz-css-j47apk brz-css-1qq0lr1 brz-css-bbd1a0" href="mt-info" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="yuzprtehfippxsxhvkduelaepistuywvrxsj"><svg class="brz-icon-svg align-[initial] brz-css-8l4ene brz-css-1qzfva0"><use href="assets/svg/27491414ce19b6aef50d991f600a4d39.svg#nc_icon"></use></svg><span data-brz-translate-text="1" class="brz-span brz-text__editor">Για Μουσικοθεραπευτές</span></a></div></div></div></div></div></div></div></div></div></div></div></section><section id="footer" class="brz-section brz-css-2sdt19 brz-css-1ij56rs"><div class="brz-section__content brz-section--boxed brz-css-guytnn brz-css-1xl9s1b" data-brz-custom-id="wqruexykkmfstbrfmiurpgxhnsclaugxhksu"><div class="brz-bg"><div class="brz-bg-color"></div></div><div class="brz-container brz-css-1luw0hj brz-css-8dnozk"><div class="brz-row__container brz-css-10e8jpe brz-css-1an4vvi" data-brz-custom-id="yppokrsdetlojfjagtucgccaloaggqmqduhq"><div class="brz-row brz-css-1mgbab6 brz-css-187yhb2 brz-css-1gohfum"><div class="brz-columns brz-css-1vf6zvc brz-css-llznuj" data-brz-custom-id="x4IjefNauN3D"><div class="brz-column__items brz-css-m57ec7 brz-css-1z06xgw"><div id="" class="brz-css-1u7d53e brz-css-jvzud0 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-vuz851" data-brz-custom-id="dV6b7WWrpSiP"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_5 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-20 brz-ft-google brz-ff-overpass brz-text-lg-right brz-text-xs-justify brz-tp-lg-subtitle brz-css-nwoKe" data-generated-css="brz-css-kzQEQ" data-uniq-id="v97od"><span class="brz-cp-color2">Σύλλογος</span></p></div></div></div><div id="" class="brz-css-1u7d53e brz-css-slzu2l brz-css-1b6471k brz-wrapper"><div class="brz-spacer brz-css-z59yr brz-css-h5jkn6"></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-18y62i7" data-brz-custom-id="kX2FdVvJQkWS"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-1yvwm5t brz-css-pa6524 brz-css-miyt54" href="mt-blog.html" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="uPY4NDsuOH4c"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Blog / Άρθρα</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-127ypgu" data-brz-custom-id="vbdh9x_SizJU"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-pj9c4m brz-css-pa6524 brz-css-1ekb251" href="/mt" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="mDX9DAevYMs_"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Μουσικοθεραπεία</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-y8fpl2" data-brz-custom-id="zPS3Tvt7Ldce"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-1nox22q brz-css-pa6524 brz-css-1lfjiu8" href="/find" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="hRVbnFWj_ISu"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Βρες Μουσικοθεραπευτή</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-1uq4lhf" data-brz-custom-id="ij3BTkfQfQSg"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-7pqe0i brz-css-pa6524 brz-css-5v297a" href="/study" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="fgk1bOc6VXeJ"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Σπουδές Μουσικοθεραπείας</span></a></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-1wim2wv" data-brz-custom-id="wh70JUVQU_Eo"><a class="brz-a brz-btn brz-css-1n97tm4 brz-css-l98slo brz-css-pa6524 brz-css-1aatpbp" href="mt-info" target="_self" rel="noopener" data-brz-link-type="external" data-brz-custom-id="yiM6j1zRzhT8"><span data-brz-translate-text="1" class="brz-span brz-text__editor">Πληροφορίες για Μουσικοθεραπευτές</span></a></div></div></div><div class="brz-columns brz-css-1vf6zvc brz-css-148dahv" data-brz-custom-id="crmdxghqahuloxvbtipxqendznacfsclqses"><div class="brz-column__items brz-css-m57ec7 brz-css-qweajh"><div id="" class="brz-css-1u7d53e brz-css-1y48u96 brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-tvsjwg" data-brz-custom-id="jcuwwkscafklpamntqqavghcmwcsxqykgiry"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_5 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-20 brz-ft-google brz-ff-overpass brz-text-xs-justify brz-tp-lg-subtitle brz-css-bnGyO" data-generated-css="brz-css-gyzJs" data-uniq-id="dKg17"><span class="brz-cp-color2">Επικοινωνία</span></p></div></div></div><div class="brz-wrapper-clone brz-flex-xs-wrap brz-css-facpgg brz-css-19rtcdj" data-brz-custom-id="nwujdtpjmkjqqvtdaspbhmemljbubaciwlkr"><div class="brz-icon__container" data-brz-custom-id="moyzqqiuimsadvitdlgxcoacautxfhtyqzqj"><a class="brz-a" href="https://www.facebook.com/profile.php?id=61572078845247" target="_self" rel="noopener" data-brz-link-type="external"><span class="brz-icon brz-span brz-css-sqbbv8 brz-css-j93mke"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/d710c5ce3ce8671b1521487d79654e3d.svg#nc_icon"></use></svg></span></a></div><div class="brz-icon__container" data-brz-custom-id="kzvpreckspwzepwhzkyecketzinslpohyaii"><a class="brz-a" href="https://www.instagram.com/music_therapy_cy" target="_self" rel="noopener" data-brz-link-type="external"><span class="brz-icon brz-span brz-css-sqbbv8 brz-css-gjsln7"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/207d5e57bd91d810599bb7b29b33c358.svg#nc_icon"></use></svg></span></a></div></div><div id="" class="brz-css-1u7d53e brz-css-slzu2l brz-css-1m3u1b6 brz-wrapper"><div class="brz-spacer brz-css-z59yr brz-css-zwnm8i"></div></div><div id="" class="brz-css-1u7d53e brz-css-qxobrp brz-wrapper"><div class="brz-icon-text brz-css-lc1q61 brz-css-p80si6" data-brz-custom-id="iiqubnsomhhfaerwisytfsfhpzokvodsjedn"><div class="brz-icon__container" data-brz-custom-id="rohhcnktgebfzxzztqymomoefvxgibrtaqfx"><span class="brz-icon brz-span brz-css-sqbbv8 brz-css-1w201we"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/3ab13401b5087e89fe0e4d04b6a9bcd4.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-10df8lu" data-brz-custom-id="ungzlxcfrsvtafskhsmduaaexwewvsphlzrr"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-16 brz-ft-google brz-ff-overpass brz-fs-xs-14 brz-fss-xs-px brz-fw-xs-400 brz-ls-xs-0 brz-lh-xs-1_6 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-text-xs-justify brz-tp-lg-uGuDLCdCXLbq brz-tp-xs-uGuDLCdCXLbq brz-css-qLxIb" data-generated-css="brz-css-cvCqH" data-uniq-id="vhy6J"><a class="link--external brz-cp-color7" href="tel:+35797661501" data-brz-link-type="external">+357 97 661501</a></p></div></div></div></div></div><div id="" class="brz-css-1u7d53e brz-css-7xvgik brz-wrapper"><div class="brz-icon-text brz-css-lc1q61 brz-css-33vbr4" data-brz-custom-id="othzgelrlicbowdprqlkzufpnydfesklmhvm"><div class="brz-icon__container" data-brz-custom-id="kkvgtjapfowujcgtaliixwxyzwepjryupymx"><span class="brz-icon brz-span brz-css-sqbbv8 brz-css-1hdkxre"><svg class="brz-icon-svg align-[initial]"><use href="assets/svg/3eae82f17391f20cdfc33c0710557915.svg#nc_icon"></use></svg></span></div><div class="brz-text-btn"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-mqvrob" data-brz-custom-id="fdqdynypgohpacgohzzilfaedwwousjhwppv"><div data-brz-translate-text="1"><p class="brz-fsft-lg-0 brz-fwdth-lg-100 brz-vfw-lg-400 brz-lh-lg-1_6 brz-ls-lg-0 brz-fw-lg-400 brz-fss-lg-px brz-fs-lg-16 brz-ft-google brz-ff-overpass brz-fs-xs-14 brz-fss-xs-px brz-fw-xs-400 brz-ls-xs-0 brz-lh-xs-1_6 brz-vfw-xs-400 brz-fwdth-xs-100 brz-fsft-xs-0 brz-text-xs-justify brz-tp-lg-uGuDLCdCXLbq brz-tp-xs-uGuDLCdCXLbq brz-css-fMQkB" data-generated-css="brz-css-nKI4g" data-uniq-id="pLGcD"><a class="link--external brz-cp-color7" href="mailto: <EMAIL> " data-brz-link-type="external" target="_blank"><EMAIL> </a></p></div></div></div></div></div><div id="" class="brz-css-1u7d53e brz-css-1yid6jy brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-mpjmry" data-brz-custom-id="aardekiwydvflewsszxkqgtsphtmilutznpl"><div data-brz-translate-text="1"><p class="brz-tp-xs-uGuDLCdCXLbq brz-tp-lg-uGuDLCdCXLbq brz-fsft-xs-0 brz-fwdth-xs-100 brz-vfw-xs-400 brz-lh-xs-1_8 brz-ls-xs-0 brz-fw-xs-400 brz-fss-xs-px brz-fs-xs-14 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-0 brz-lh-lg-2 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-ft-google brz-fs-lg-16 brz-ff-overpass brz-text-xs-left brz-css-y6prp" data-uniq-id="fXT1V" data-generated-css="brz-css-aE7oZ"><span class="brz-cp-color7">Οινόης 7, Στρόβολος 2037 Λευκωσία</span></p></div></div></div></div></div></div></div><div id="" class="brz-css-1u7d53e brz-css-slzu2l brz-css-mbir7l brz-wrapper"><div class="brz-spacer brz-css-z59yr brz-css-1l3j2t3"></div></div><div id="" class="brz-css-1u7d53e brz-css-106cr9e brz-wrapper"><div class="brz-rich-text brz-rich-text__custom brz-css-1pjdngh brz-css-o4546m" data-brz-custom-id="uhvdsajaaizzsumbtpczrowasicltdzgvjlc"><div data-brz-translate-text="1"><p class="brz-tp-lg-uGuDLCdCXLbq brz-text-lg-center brz-ff-overpass brz-ft-google brz-fs-lg-16 brz-fss-lg-px brz-fw-lg-400 brz-ls-lg-0 brz-lh-lg-1_6 brz-vfw-lg-400 brz-fwdth-lg-100 brz-fsft-lg-0 brz-tp-xs-u9Xihr8QXhSs brz-css-gyvoe" data-uniq-id="jJY6l" data-generated-css="brz-css-eBk08"><span class="brz-cp-color7">© 2025 Cyprus Music Therapy Association. </span></p></div></div></div></div></div></section>     </div>          </body></html>